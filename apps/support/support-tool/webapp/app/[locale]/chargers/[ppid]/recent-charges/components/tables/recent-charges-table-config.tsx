import {
  Button,
  ButtonPadding,
  ButtonTypes,
  CheckmarkCircleIcon,
  CrossCircleIcon,
  CurrencyPoundIcon,
  PencilIcon,
  Placement,
  Tooltip,
} from '@experience/shared/react/design-system';
import { ChargingStationSummary } from '@experience/shared/axios/assets-api-client';
import { ColumnDef } from '@tanstack/react-table';
import {
  OidcRoles,
  isArch5Charger,
} from '@experience/support/support-tool/shared';
import { Charges2 as RecentCharge } from '@experience/shared/axios/data-platform-api-client';
import {
  formatDurationToTime,
  formatPenceAsCurrencyString,
  formatSecondsToTime,
} from '@experience/shared/typescript/utils';
import { useFormatter, useTranslations } from 'next-intl';
import { useRole } from '../../../../../roles/store';
import dayjs from 'dayjs';

const formatTableDate = (date?: string) =>
  date ? dayjs(date).format('YYYY-MM-DD - HH:mm:ss') : '-';

export const useRecentChargeTableConfig = (
  handleOpenEditEnergyCost: (charge: RecentCharge) => void,
  handleOpenEditRevenueCost: (charge: RecentCharge) => void,
  isCommercial = false,
  summary: ChargingStationSummary
): ColumnDef<RecentCharge>[] => {
  const t = useTranslations('Chargers.RecentChargesPage');
  const format = useFormatter();

  const canUpdateRevenueCost = useRole(OidcRoles.CHARGE_EDIT_REVENUE);

  return [
    {
      accessorKey: 'pluggedInAt',
      cell: ({ row: { original: charge } }) =>
        formatTableDate(charge.pluggedInAt),
      header: () => t('pluggedInAtHeader'),
      meta: { cellClassName: 'text-nowrap' },
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'startedAt',
      cell: ({ row: { original: charge } }) =>
        formatTableDate(charge.startedAt),
      header: () => t('startedAtHeader'),
      meta: { cellClassName: 'text-nowrap' },
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'endedAt',
      cell: ({ row: { original: charge } }) => formatTableDate(charge.endedAt),
      header: () => t('endedAtHeader'),
      meta: { cellClassName: 'text-nowrap' },
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'unpluggedAt',
      cell: ({ row: { original: charge } }) =>
        formatTableDate(charge.unpluggedAt),
      header: () => t('unpluggedAtHeader'),
      meta: { cellClassName: 'text-nowrap' },
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'energyTotal',
      header: () => t('energyTotalHeader'),
      cell: ({ row: { original: charge } }) =>
        format.number(charge.energyTotal),
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'gridEnergyTotal',
      header: () => t('gridEnergyTotalHeader'),
      cell: ({ row: { original: charge } }) =>
        !isArch5Charger(summary)
          ? 'N/A'
          : format.number(charge.gridEnergyTotal),
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'generationEnergyTotal',
      header: () => t('generationEnergyTotalHeader'),
      cell: ({ row: { original: charge } }) =>
        !isArch5Charger(summary)
          ? 'N/A'
          : format.number(charge.generationEnergyTotal),
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'energyCost',
      cell: ({ row: { original: charge } }) =>
        formatPenceAsCurrencyString({ amount: charge.energyCost }),
      header: () => t('energyCostHeader'),
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'revenueGenerated',
      cell: ({ row: { original: charge } }) =>
        formatPenceAsCurrencyString({ amount: charge.revenueGenerated }),
      header: () => t('revenueGeneratedHeader'),
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'chargeDurationTotal',
      cell: ({ row: { original: charge } }) =>
        charge.chargeDurationTotal
          ? formatSecondsToTime(charge.chargeDurationTotal)
          : '-',
      header: () => t('chargeDurationTotalHeader'),
    },
    {
      id: 'totalDuration',
      cell: ({ row: { original: charge } }) =>
        formatDurationToTime(charge.pluggedInAt, charge.unpluggedAt),
      header: () => t('totalDurationHeader'),
    },
    {
      accessorKey: 'door',
      header: () => t('doorHeader'),
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'confirmed',
      cell: ({ row: { original: charge } }) =>
        charge.confirmed ? (
          <CheckmarkCircleIcon.LIGHT
            className="mx-auto text-primary"
            height="h-6"
            title="Yes"
            width="w-6"
          />
        ) : (
          <CrossCircleIcon.LIGHT
            className="mx-auto text-error"
            height="h-6"
            title="No"
            width="w-6"
          />
        ),
      header: () => t('confirmedHeader'),
      sortingFn: 'alphanumeric',
    },
    {
      accessorKey: 'actions',
      cell: ({ row: { original: charge } }) => (
        <div className="flex space-x-1">
          <Tooltip
            message={t('actionsEditEnergyTooltip')}
            placement={Placement.TOP}
          >
            <div className="flex justify-center mr-4">
              <Button
                aria-label={t('actionsEditEnergyAriaLabel', {
                  chargeId: charge.id,
                })}
                buttonType={ButtonTypes.LINK}
                id={`edit-energy-cost-${charge.id}`}
                name={`edit-energy--cost-${charge.id}`}
                onClick={() => handleOpenEditEnergyCost(charge)}
                padding={ButtonPadding.BOX}
              >
                <PencilIcon.LIGHT />
              </Button>
            </div>
          </Tooltip>
          {isCommercial && canUpdateRevenueCost ? (
            <Tooltip
              message={t('actionsEditRevenueCostTooltip')}
              placement={Placement.TOP}
            >
              <div className="flex justify-center mr-4">
                <Button
                  aria-label={t('actionsEditRevenueCostAriaLabel', {
                    chargeId: charge.id,
                  })}
                  buttonType={ButtonTypes.LINK}
                  id={`edit-revenue-cost-${charge.id}`}
                  name={`edit-revenue--cost-${charge.id}`}
                  onClick={() => handleOpenEditRevenueCost(charge)}
                  padding={ButtonPadding.BOX}
                >
                  <CurrencyPoundIcon.LIGHT />
                </Button>
              </div>
            </Tooltip>
          ) : null}
        </div>
      ),
      header: () => t('actionsHeader'),
    },
  ];
};

export default useRecentChargeTableConfig;
