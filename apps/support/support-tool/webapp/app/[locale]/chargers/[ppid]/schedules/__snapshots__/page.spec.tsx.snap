// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`edit charger schedules page should match snapshot when delegated control is active 1`] = `
<body>
  <div>
    <a
      class="text-info hover:underline hover:text-info active:text-info cursor-pointer flex items-center gap-1 mb-2 font-bold max-w-fit"
      href="/chargers/PSL-12345"
    >
      <svg
        class="fill-current w-4 h-4"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g
          fill="none"
        >
          <path
            d="M9.14 4L1 12.15L9.6 20.75"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
          <path
            d="M22.88 12.13H1"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
        </g>
      </svg>
      Back
    </a>
    <section
      class="p-3 rounded-lg bg-white"
    >
      <header>
        <div
          class="flex"
        >
          <h1
            class="text-xxl font-bold"
          >
            View schedules - PSL-12345
          </h1>
          <div
            class="ml-auto"
          />
        </div>
      </header>
    </section>
    <div
      class="pb-4"
    />
    <div
      class="rounded-md p-3 bg-info/10 border-2 border-info/30"
    >
      <div
        class="flex"
      >
        <div
          class="shrink-0"
        >
          <span
            class="text-info"
          >
            <svg
              class="fill-current w-6 h-6"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                />
                <path
                  d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                />
                <path
                  d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                />
              </g>
            </svg>
          </span>
        </div>
        <div
          class="flex flex-col justify-center ml-4"
        >
          <p
            class="text-md font-normal sr-only break-words"
          >
            Alert
          </p>
          <div
            class="text-info"
          >
            <p
              class="text-md font-normal break-words"
            >
              This charger is currently under delegated control. The below schedules are indicative only and subject to change.
            </p>
          </div>
        </div>
      </div>
    </div>
    <div
      class="pb-4"
    />
    <section
      class="p-3 rounded-lg bg-white"
    >
      <div
        class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
      >
        <div
          class="inline-block min-w-full -my-2 py-2 align-middle"
        >
          <table
            class="min-w-full"
            tabindex="0"
          >
            <caption
              class="sr-only"
            >
              view-schedule-table
            </caption>
            <thead
              class="border-b border-b-neutral/20"
            >
              <tr
                class="border-b-neutral/20"
              >
                <th
                  class="text-left p-3"
                  scope="col"
                >
                  Day
                </th>
                <th
                  class="text-left p-3"
                  scope="col"
                >
                  Start
                </th>
                <th
                  class="text-left p-3"
                  scope="col"
                >
                  Duration
                </th>
                <th
                  class="text-left text-center p-3"
                  scope="col"
                >
                  Active
                </th>
              </tr>
            </thead>
            <tbody
              class="divide-y border-b border-b-neutral/20"
            >
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  Monday
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  00:00:00
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  5 hours
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  <svg
                    class="fill-current h-6 w-6 mx-auto text-error"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      No
                    </title>
                    <g>
                      <path
                        d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                      />
                      <path
                        d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                      />
                    </g>
                  </svg>
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  Tuesday
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  00:00:00
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  5 hours
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  <svg
                    class="fill-current h-6 w-6 mx-auto text-error"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      No
                    </title>
                    <g>
                      <path
                        d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                      />
                      <path
                        d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                      />
                    </g>
                  </svg>
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  Wednesday
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  00:00:00
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  5 hours
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  <svg
                    class="fill-current h-6 w-6 mx-auto text-error"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      No
                    </title>
                    <g>
                      <path
                        d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                      />
                      <path
                        d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                      />
                    </g>
                  </svg>
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  Thursday
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  00:00:00
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  5 hours
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  <svg
                    class="fill-current h-6 w-6 mx-auto text-error"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      No
                    </title>
                    <g>
                      <path
                        d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                      />
                      <path
                        d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                      />
                    </g>
                  </svg>
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  Friday
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  00:00:00
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  5 hours
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  <svg
                    class="fill-current h-6 w-6 mx-auto text-error"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      No
                    </title>
                    <g>
                      <path
                        d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                      />
                      <path
                        d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                      />
                    </g>
                  </svg>
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  Saturday
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  00:00:00
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  5 hours
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  <svg
                    class="fill-current h-6 w-6 mx-auto text-error"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      No
                    </title>
                    <g>
                      <path
                        d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                      />
                      <path
                        d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                      />
                    </g>
                  </svg>
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  Sunday
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  00:00:00
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  5 hours
                </td>
                <td
                  class="whitespace-normal px-3 py-4"
                >
                  <svg
                    class="fill-current h-6 w-6 mx-auto text-error"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      No
                    </title>
                    <g>
                      <path
                        d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                      />
                      <path
                        d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                      />
                    </g>
                  </svg>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>
  </div>
</body>
`;

exports[`edit charger schedules page should match snapshot when delegated control is inactive 1`] = `
<body>
  <div>
    <a
      class="text-info hover:underline hover:text-info active:text-info cursor-pointer flex items-center gap-1 mb-2 font-bold max-w-fit"
      href="/chargers/PSL-12345"
    >
      <svg
        class="fill-current w-4 h-4"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g
          fill="none"
        >
          <path
            d="M9.14 4L1 12.15L9.6 20.75"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
          <path
            d="M22.88 12.13H1"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
        </g>
      </svg>
      Back
    </a>
    <section
      class="p-3 rounded-lg bg-white"
    >
      <header>
        <div
          class="flex"
        >
          <h1
            class="text-xxl font-bold"
          >
            Edit schedules - PSL-12345
          </h1>
          <div
            class="ml-auto"
          />
        </div>
      </header>
    </section>
    <div
      class="pb-4"
    />
    <div
      class="rounded-md p-3 bg-info/10 border-2 border-info/30"
    >
      <div
        class="flex"
      >
        <div
          class="shrink-0"
        >
          <span
            class="text-info"
          >
            <svg
              class="fill-current w-6 h-6"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                />
                <path
                  d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                />
                <path
                  d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                />
              </g>
            </svg>
          </span>
        </div>
        <div
          class="flex flex-col justify-center ml-4"
        >
          <p
            class="text-md font-normal sr-only break-words"
          >
            Alert
          </p>
          <div
            class="text-info"
          >
            <p
              class="text-md font-normal break-words"
            >
              This charger is currently in manual mode. The below schedules will not take effect.
            </p>
          </div>
        </div>
      </div>
    </div>
    <div
      class="pb-4"
    />
    <form>
      <input
        name="ppid"
        type="hidden"
        value="PSL-12345"
      />
      <section
        class="p-3 rounded-lg bg-white"
      >
        <div
          class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                edit-schedule-table
              </caption>
              <thead
                class="border-b border-b-neutral/20"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <th
                    class="text-left p-3"
                    scope="col"
                  />
                  <th
                    class="text-left p-3"
                    scope="col"
                  >
                    Start
                  </th>
                  <th
                    class="text-left p-3"
                    colspan="4"
                    scope="col"
                  >
                    Duration
                  </th>
                  <th
                    class="text-left p-3"
                    scope="col"
                  >
                    Active
                  </th>
                </tr>
              </thead>
              <tbody
                class="divide-y border-b border-b-neutral/20"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-bold break-words"
                    >
                      Monday
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class=""
                    >
                      <label
                        class="text-md font-bold sr-only block mb-2"
                        for="Monday-startTime"
                      >
                        Monday-startTime
                      </label>
                    </div>
                    <input
                      class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10"
                      id="Monday-startTime"
                      name="Monday-startTime"
                      type="time"
                      value="00:00:00"
                    />
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      data-headlessui-state=""
                    >
                      <label
                        aria-labelledby="Monday-hours"
                        class="block mb-2 sr-only text-md font-bold"
                        data-headlessui-state=""
                        for="Monday-hours"
                        id="headlessui-label-:test-id-1"
                      >
                        Monday-hours
                      </label>
                      <div
                        class="relative mt-1"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="listbox"
                          aria-labelledby="headlessui-label-:test-id-1 Monday-hours"
                          class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10 w-2/3"
                          data-headlessui-state=""
                          id="Monday-hours"
                          type="button"
                        >
                          <span
                            class="block truncate"
                          >
                            5
                          </span>
                          <span
                            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                          >
                            <svg
                              aria-hidden="true"
                              class="fill-current w-4 h-2.5 text-neutral"
                              viewBox="0 0 32 32"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <title>
                                drop down
                              </title>
                              <g>
                                <path
                                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                                />
                              </g>
                            </svg>
                          </span>
                        </button>
                      </div>
                      <span
                        hidden=""
                        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                      >
                        <input
                          hidden=""
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                        />
                        <input
                          hidden=""
                          name="Monday-hours"
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                          value="5"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    hours
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      data-headlessui-state=""
                    >
                      <label
                        aria-labelledby="Monday-minutes"
                        class="block mb-2 sr-only text-md font-bold"
                        data-headlessui-state=""
                        for="Monday-minutes"
                        id="headlessui-label-:test-id-9"
                      >
                        Monday-minutes
                      </label>
                      <div
                        class="relative mt-1"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="listbox"
                          aria-labelledby="headlessui-label-:test-id-9 Monday-minutes"
                          class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10"
                          data-headlessui-state=""
                          id="Monday-minutes"
                          type="button"
                        >
                          <span
                            class="block truncate"
                          >
                            0
                          </span>
                          <span
                            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                          >
                            <svg
                              aria-hidden="true"
                              class="fill-current w-4 h-2.5 text-neutral"
                              viewBox="0 0 32 32"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <title>
                                drop down
                              </title>
                              <g>
                                <path
                                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                                />
                              </g>
                            </svg>
                          </span>
                        </button>
                      </div>
                      <span
                        hidden=""
                        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                      >
                        <input
                          hidden=""
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                        />
                        <input
                          hidden=""
                          name="Monday-minutes"
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                          value="0"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    minutes
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex"
                    >
                      <div
                        data-headlessui-state=""
                      >
                        <div
                          class="mr-2"
                        >
                          <label
                            class="text-md font-bold"
                            for="Monday-active"
                            id="Monday-active-label"
                          />
                        </div>
                        <button
                          aria-checked="false"
                          aria-label="Monday-active"
                          class="bg-neutral/20 group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-info/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                          data-headlessui-state=""
                          id="Monday-active"
                          role="switch"
                          tabindex="0"
                          type="button"
                        >
                          <span
                            aria-hidden="true"
                            class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                          />
                        </button>
                        <span
                          hidden=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                        >
                          <input
                            hidden=""
                            readonly=""
                            style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            type="hidden"
                          />
                          <input
                            hidden=""
                            name="Monday-active"
                            readonly=""
                            style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            type="checkbox"
                            value="on"
                          />
                        </span>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-bold break-words"
                    >
                      Tuesday
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class=""
                    >
                      <label
                        class="text-md font-bold sr-only block mb-2"
                        for="Tuesday-startTime"
                      >
                        Tuesday-startTime
                      </label>
                    </div>
                    <input
                      class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10"
                      id="Tuesday-startTime"
                      name="Tuesday-startTime"
                      type="time"
                      value="00:00:00"
                    />
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      data-headlessui-state=""
                    >
                      <label
                        aria-labelledby="Tuesday-hours"
                        class="block mb-2 sr-only text-md font-bold"
                        data-headlessui-state=""
                        for="Tuesday-hours"
                        id="headlessui-label-:test-id-19"
                      >
                        Tuesday-hours
                      </label>
                      <div
                        class="relative mt-1"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="listbox"
                          aria-labelledby="headlessui-label-:test-id-19 Tuesday-hours"
                          class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10 w-2/3"
                          data-headlessui-state=""
                          id="Tuesday-hours"
                          type="button"
                        >
                          <span
                            class="block truncate"
                          >
                            5
                          </span>
                          <span
                            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                          >
                            <svg
                              aria-hidden="true"
                              class="fill-current w-4 h-2.5 text-neutral"
                              viewBox="0 0 32 32"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <title>
                                drop down
                              </title>
                              <g>
                                <path
                                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                                />
                              </g>
                            </svg>
                          </span>
                        </button>
                      </div>
                      <span
                        hidden=""
                        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                      >
                        <input
                          hidden=""
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                        />
                        <input
                          hidden=""
                          name="Tuesday-hours"
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                          value="5"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    hours
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      data-headlessui-state=""
                    >
                      <label
                        aria-labelledby="Tuesday-minutes"
                        class="block mb-2 sr-only text-md font-bold"
                        data-headlessui-state=""
                        for="Tuesday-minutes"
                        id="headlessui-label-:test-id-27"
                      >
                        Tuesday-minutes
                      </label>
                      <div
                        class="relative mt-1"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="listbox"
                          aria-labelledby="headlessui-label-:test-id-27 Tuesday-minutes"
                          class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10"
                          data-headlessui-state=""
                          id="Tuesday-minutes"
                          type="button"
                        >
                          <span
                            class="block truncate"
                          >
                            0
                          </span>
                          <span
                            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                          >
                            <svg
                              aria-hidden="true"
                              class="fill-current w-4 h-2.5 text-neutral"
                              viewBox="0 0 32 32"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <title>
                                drop down
                              </title>
                              <g>
                                <path
                                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                                />
                              </g>
                            </svg>
                          </span>
                        </button>
                      </div>
                      <span
                        hidden=""
                        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                      >
                        <input
                          hidden=""
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                        />
                        <input
                          hidden=""
                          name="Tuesday-minutes"
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                          value="0"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    minutes
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex"
                    >
                      <div
                        data-headlessui-state=""
                      >
                        <div
                          class="mr-2"
                        >
                          <label
                            class="text-md font-bold"
                            for="Tuesday-active"
                            id="Tuesday-active-label"
                          />
                        </div>
                        <button
                          aria-checked="false"
                          aria-label="Tuesday-active"
                          class="bg-neutral/20 group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-info/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                          data-headlessui-state=""
                          id="Tuesday-active"
                          role="switch"
                          tabindex="0"
                          type="button"
                        >
                          <span
                            aria-hidden="true"
                            class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                          />
                        </button>
                        <span
                          hidden=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                        >
                          <input
                            hidden=""
                            readonly=""
                            style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            type="hidden"
                          />
                          <input
                            hidden=""
                            name="Tuesday-active"
                            readonly=""
                            style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            type="checkbox"
                            value="on"
                          />
                        </span>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-bold break-words"
                    >
                      Wednesday
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class=""
                    >
                      <label
                        class="text-md font-bold sr-only block mb-2"
                        for="Wednesday-startTime"
                      >
                        Wednesday-startTime
                      </label>
                    </div>
                    <input
                      class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10"
                      id="Wednesday-startTime"
                      name="Wednesday-startTime"
                      type="time"
                      value="00:00:00"
                    />
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      data-headlessui-state=""
                    >
                      <label
                        aria-labelledby="Wednesday-hours"
                        class="block mb-2 sr-only text-md font-bold"
                        data-headlessui-state=""
                        for="Wednesday-hours"
                        id="headlessui-label-:test-id-37"
                      >
                        Wednesday-hours
                      </label>
                      <div
                        class="relative mt-1"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="listbox"
                          aria-labelledby="headlessui-label-:test-id-37 Wednesday-hours"
                          class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10 w-2/3"
                          data-headlessui-state=""
                          id="Wednesday-hours"
                          type="button"
                        >
                          <span
                            class="block truncate"
                          >
                            5
                          </span>
                          <span
                            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                          >
                            <svg
                              aria-hidden="true"
                              class="fill-current w-4 h-2.5 text-neutral"
                              viewBox="0 0 32 32"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <title>
                                drop down
                              </title>
                              <g>
                                <path
                                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                                />
                              </g>
                            </svg>
                          </span>
                        </button>
                      </div>
                      <span
                        hidden=""
                        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                      >
                        <input
                          hidden=""
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                        />
                        <input
                          hidden=""
                          name="Wednesday-hours"
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                          value="5"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    hours
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      data-headlessui-state=""
                    >
                      <label
                        aria-labelledby="Wednesday-minutes"
                        class="block mb-2 sr-only text-md font-bold"
                        data-headlessui-state=""
                        for="Wednesday-minutes"
                        id="headlessui-label-:test-id-45"
                      >
                        Wednesday-minutes
                      </label>
                      <div
                        class="relative mt-1"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="listbox"
                          aria-labelledby="headlessui-label-:test-id-45 Wednesday-minutes"
                          class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10"
                          data-headlessui-state=""
                          id="Wednesday-minutes"
                          type="button"
                        >
                          <span
                            class="block truncate"
                          >
                            0
                          </span>
                          <span
                            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                          >
                            <svg
                              aria-hidden="true"
                              class="fill-current w-4 h-2.5 text-neutral"
                              viewBox="0 0 32 32"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <title>
                                drop down
                              </title>
                              <g>
                                <path
                                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                                />
                              </g>
                            </svg>
                          </span>
                        </button>
                      </div>
                      <span
                        hidden=""
                        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                      >
                        <input
                          hidden=""
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                        />
                        <input
                          hidden=""
                          name="Wednesday-minutes"
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                          value="0"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    minutes
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex"
                    >
                      <div
                        data-headlessui-state=""
                      >
                        <div
                          class="mr-2"
                        >
                          <label
                            class="text-md font-bold"
                            for="Wednesday-active"
                            id="Wednesday-active-label"
                          />
                        </div>
                        <button
                          aria-checked="false"
                          aria-label="Wednesday-active"
                          class="bg-neutral/20 group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-info/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                          data-headlessui-state=""
                          id="Wednesday-active"
                          role="switch"
                          tabindex="0"
                          type="button"
                        >
                          <span
                            aria-hidden="true"
                            class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                          />
                        </button>
                        <span
                          hidden=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                        >
                          <input
                            hidden=""
                            readonly=""
                            style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            type="hidden"
                          />
                          <input
                            hidden=""
                            name="Wednesday-active"
                            readonly=""
                            style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            type="checkbox"
                            value="on"
                          />
                        </span>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-bold break-words"
                    >
                      Thursday
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class=""
                    >
                      <label
                        class="text-md font-bold sr-only block mb-2"
                        for="Thursday-startTime"
                      >
                        Thursday-startTime
                      </label>
                    </div>
                    <input
                      class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10"
                      id="Thursday-startTime"
                      name="Thursday-startTime"
                      type="time"
                      value="00:00:00"
                    />
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      data-headlessui-state=""
                    >
                      <label
                        aria-labelledby="Thursday-hours"
                        class="block mb-2 sr-only text-md font-bold"
                        data-headlessui-state=""
                        for="Thursday-hours"
                        id="headlessui-label-:test-id-55"
                      >
                        Thursday-hours
                      </label>
                      <div
                        class="relative mt-1"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="listbox"
                          aria-labelledby="headlessui-label-:test-id-55 Thursday-hours"
                          class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10 w-2/3"
                          data-headlessui-state=""
                          id="Thursday-hours"
                          type="button"
                        >
                          <span
                            class="block truncate"
                          >
                            5
                          </span>
                          <span
                            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                          >
                            <svg
                              aria-hidden="true"
                              class="fill-current w-4 h-2.5 text-neutral"
                              viewBox="0 0 32 32"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <title>
                                drop down
                              </title>
                              <g>
                                <path
                                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                                />
                              </g>
                            </svg>
                          </span>
                        </button>
                      </div>
                      <span
                        hidden=""
                        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                      >
                        <input
                          hidden=""
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                        />
                        <input
                          hidden=""
                          name="Thursday-hours"
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                          value="5"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    hours
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      data-headlessui-state=""
                    >
                      <label
                        aria-labelledby="Thursday-minutes"
                        class="block mb-2 sr-only text-md font-bold"
                        data-headlessui-state=""
                        for="Thursday-minutes"
                        id="headlessui-label-:test-id-63"
                      >
                        Thursday-minutes
                      </label>
                      <div
                        class="relative mt-1"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="listbox"
                          aria-labelledby="headlessui-label-:test-id-63 Thursday-minutes"
                          class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10"
                          data-headlessui-state=""
                          id="Thursday-minutes"
                          type="button"
                        >
                          <span
                            class="block truncate"
                          >
                            0
                          </span>
                          <span
                            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                          >
                            <svg
                              aria-hidden="true"
                              class="fill-current w-4 h-2.5 text-neutral"
                              viewBox="0 0 32 32"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <title>
                                drop down
                              </title>
                              <g>
                                <path
                                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                                />
                              </g>
                            </svg>
                          </span>
                        </button>
                      </div>
                      <span
                        hidden=""
                        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                      >
                        <input
                          hidden=""
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                        />
                        <input
                          hidden=""
                          name="Thursday-minutes"
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                          value="0"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    minutes
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex"
                    >
                      <div
                        data-headlessui-state=""
                      >
                        <div
                          class="mr-2"
                        >
                          <label
                            class="text-md font-bold"
                            for="Thursday-active"
                            id="Thursday-active-label"
                          />
                        </div>
                        <button
                          aria-checked="false"
                          aria-label="Thursday-active"
                          class="bg-neutral/20 group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-info/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                          data-headlessui-state=""
                          id="Thursday-active"
                          role="switch"
                          tabindex="0"
                          type="button"
                        >
                          <span
                            aria-hidden="true"
                            class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                          />
                        </button>
                        <span
                          hidden=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                        >
                          <input
                            hidden=""
                            readonly=""
                            style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            type="hidden"
                          />
                          <input
                            hidden=""
                            name="Thursday-active"
                            readonly=""
                            style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            type="checkbox"
                            value="on"
                          />
                        </span>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-bold break-words"
                    >
                      Friday
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class=""
                    >
                      <label
                        class="text-md font-bold sr-only block mb-2"
                        for="Friday-startTime"
                      >
                        Friday-startTime
                      </label>
                    </div>
                    <input
                      class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10"
                      id="Friday-startTime"
                      name="Friday-startTime"
                      type="time"
                      value="00:00:00"
                    />
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      data-headlessui-state=""
                    >
                      <label
                        aria-labelledby="Friday-hours"
                        class="block mb-2 sr-only text-md font-bold"
                        data-headlessui-state=""
                        for="Friday-hours"
                        id="headlessui-label-:test-id-73"
                      >
                        Friday-hours
                      </label>
                      <div
                        class="relative mt-1"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="listbox"
                          aria-labelledby="headlessui-label-:test-id-73 Friday-hours"
                          class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10 w-2/3"
                          data-headlessui-state=""
                          id="Friday-hours"
                          type="button"
                        >
                          <span
                            class="block truncate"
                          >
                            5
                          </span>
                          <span
                            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                          >
                            <svg
                              aria-hidden="true"
                              class="fill-current w-4 h-2.5 text-neutral"
                              viewBox="0 0 32 32"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <title>
                                drop down
                              </title>
                              <g>
                                <path
                                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                                />
                              </g>
                            </svg>
                          </span>
                        </button>
                      </div>
                      <span
                        hidden=""
                        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                      >
                        <input
                          hidden=""
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                        />
                        <input
                          hidden=""
                          name="Friday-hours"
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                          value="5"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    hours
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      data-headlessui-state=""
                    >
                      <label
                        aria-labelledby="Friday-minutes"
                        class="block mb-2 sr-only text-md font-bold"
                        data-headlessui-state=""
                        for="Friday-minutes"
                        id="headlessui-label-:test-id-81"
                      >
                        Friday-minutes
                      </label>
                      <div
                        class="relative mt-1"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="listbox"
                          aria-labelledby="headlessui-label-:test-id-81 Friday-minutes"
                          class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10"
                          data-headlessui-state=""
                          id="Friday-minutes"
                          type="button"
                        >
                          <span
                            class="block truncate"
                          >
                            0
                          </span>
                          <span
                            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                          >
                            <svg
                              aria-hidden="true"
                              class="fill-current w-4 h-2.5 text-neutral"
                              viewBox="0 0 32 32"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <title>
                                drop down
                              </title>
                              <g>
                                <path
                                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                                />
                              </g>
                            </svg>
                          </span>
                        </button>
                      </div>
                      <span
                        hidden=""
                        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                      >
                        <input
                          hidden=""
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                        />
                        <input
                          hidden=""
                          name="Friday-minutes"
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                          value="0"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    minutes
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex"
                    >
                      <div
                        data-headlessui-state=""
                      >
                        <div
                          class="mr-2"
                        >
                          <label
                            class="text-md font-bold"
                            for="Friday-active"
                            id="Friday-active-label"
                          />
                        </div>
                        <button
                          aria-checked="false"
                          aria-label="Friday-active"
                          class="bg-neutral/20 group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-info/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                          data-headlessui-state=""
                          id="Friday-active"
                          role="switch"
                          tabindex="0"
                          type="button"
                        >
                          <span
                            aria-hidden="true"
                            class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                          />
                        </button>
                        <span
                          hidden=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                        >
                          <input
                            hidden=""
                            readonly=""
                            style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            type="hidden"
                          />
                          <input
                            hidden=""
                            name="Friday-active"
                            readonly=""
                            style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            type="checkbox"
                            value="on"
                          />
                        </span>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-bold break-words"
                    >
                      Saturday
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class=""
                    >
                      <label
                        class="text-md font-bold sr-only block mb-2"
                        for="Saturday-startTime"
                      >
                        Saturday-startTime
                      </label>
                    </div>
                    <input
                      class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10"
                      id="Saturday-startTime"
                      name="Saturday-startTime"
                      type="time"
                      value="00:00:00"
                    />
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      data-headlessui-state=""
                    >
                      <label
                        aria-labelledby="Saturday-hours"
                        class="block mb-2 sr-only text-md font-bold"
                        data-headlessui-state=""
                        for="Saturday-hours"
                        id="headlessui-label-:test-id-91"
                      >
                        Saturday-hours
                      </label>
                      <div
                        class="relative mt-1"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="listbox"
                          aria-labelledby="headlessui-label-:test-id-91 Saturday-hours"
                          class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10 w-2/3"
                          data-headlessui-state=""
                          id="Saturday-hours"
                          type="button"
                        >
                          <span
                            class="block truncate"
                          >
                            5
                          </span>
                          <span
                            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                          >
                            <svg
                              aria-hidden="true"
                              class="fill-current w-4 h-2.5 text-neutral"
                              viewBox="0 0 32 32"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <title>
                                drop down
                              </title>
                              <g>
                                <path
                                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                                />
                              </g>
                            </svg>
                          </span>
                        </button>
                      </div>
                      <span
                        hidden=""
                        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                      >
                        <input
                          hidden=""
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                        />
                        <input
                          hidden=""
                          name="Saturday-hours"
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                          value="5"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    hours
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      data-headlessui-state=""
                    >
                      <label
                        aria-labelledby="Saturday-minutes"
                        class="block mb-2 sr-only text-md font-bold"
                        data-headlessui-state=""
                        for="Saturday-minutes"
                        id="headlessui-label-:test-id-99"
                      >
                        Saturday-minutes
                      </label>
                      <div
                        class="relative mt-1"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="listbox"
                          aria-labelledby="headlessui-label-:test-id-99 Saturday-minutes"
                          class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10"
                          data-headlessui-state=""
                          id="Saturday-minutes"
                          type="button"
                        >
                          <span
                            class="block truncate"
                          >
                            0
                          </span>
                          <span
                            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                          >
                            <svg
                              aria-hidden="true"
                              class="fill-current w-4 h-2.5 text-neutral"
                              viewBox="0 0 32 32"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <title>
                                drop down
                              </title>
                              <g>
                                <path
                                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                                />
                              </g>
                            </svg>
                          </span>
                        </button>
                      </div>
                      <span
                        hidden=""
                        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                      >
                        <input
                          hidden=""
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                        />
                        <input
                          hidden=""
                          name="Saturday-minutes"
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                          value="0"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    minutes
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex"
                    >
                      <div
                        data-headlessui-state=""
                      >
                        <div
                          class="mr-2"
                        >
                          <label
                            class="text-md font-bold"
                            for="Saturday-active"
                            id="Saturday-active-label"
                          />
                        </div>
                        <button
                          aria-checked="false"
                          aria-label="Saturday-active"
                          class="bg-neutral/20 group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-info/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                          data-headlessui-state=""
                          id="Saturday-active"
                          role="switch"
                          tabindex="0"
                          type="button"
                        >
                          <span
                            aria-hidden="true"
                            class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                          />
                        </button>
                        <span
                          hidden=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                        >
                          <input
                            hidden=""
                            readonly=""
                            style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            type="hidden"
                          />
                          <input
                            hidden=""
                            name="Saturday-active"
                            readonly=""
                            style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            type="checkbox"
                            value="on"
                          />
                        </span>
                      </div>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-bold break-words"
                    >
                      Sunday
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class=""
                    >
                      <label
                        class="text-md font-bold sr-only block mb-2"
                        for="Sunday-startTime"
                      >
                        Sunday-startTime
                      </label>
                    </div>
                    <input
                      class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10"
                      id="Sunday-startTime"
                      name="Sunday-startTime"
                      type="time"
                      value="00:00:00"
                    />
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      data-headlessui-state=""
                    >
                      <label
                        aria-labelledby="Sunday-hours"
                        class="block mb-2 sr-only text-md font-bold"
                        data-headlessui-state=""
                        for="Sunday-hours"
                        id="headlessui-label-:test-id-109"
                      >
                        Sunday-hours
                      </label>
                      <div
                        class="relative mt-1"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="listbox"
                          aria-labelledby="headlessui-label-:test-id-109 Sunday-hours"
                          class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10 w-2/3"
                          data-headlessui-state=""
                          id="Sunday-hours"
                          type="button"
                        >
                          <span
                            class="block truncate"
                          >
                            5
                          </span>
                          <span
                            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                          >
                            <svg
                              aria-hidden="true"
                              class="fill-current w-4 h-2.5 text-neutral"
                              viewBox="0 0 32 32"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <title>
                                drop down
                              </title>
                              <g>
                                <path
                                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                                />
                              </g>
                            </svg>
                          </span>
                        </button>
                      </div>
                      <span
                        hidden=""
                        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                      >
                        <input
                          hidden=""
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                        />
                        <input
                          hidden=""
                          name="Sunday-hours"
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                          value="5"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    hours
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      data-headlessui-state=""
                    >
                      <label
                        aria-labelledby="Sunday-minutes"
                        class="block mb-2 sr-only text-md font-bold"
                        data-headlessui-state=""
                        for="Sunday-minutes"
                        id="headlessui-label-:test-id-117"
                      >
                        Sunday-minutes
                      </label>
                      <div
                        class="relative mt-1"
                        data-headlessui-state=""
                      >
                        <button
                          aria-expanded="false"
                          aria-haspopup="listbox"
                          aria-labelledby="headlessui-label-:test-id-117 Sunday-minutes"
                          class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10"
                          data-headlessui-state=""
                          id="Sunday-minutes"
                          type="button"
                        >
                          <span
                            class="block truncate"
                          >
                            0
                          </span>
                          <span
                            class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                          >
                            <svg
                              aria-hidden="true"
                              class="fill-current w-4 h-2.5 text-neutral"
                              viewBox="0 0 32 32"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <title>
                                drop down
                              </title>
                              <g>
                                <path
                                  d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                                />
                              </g>
                            </svg>
                          </span>
                        </button>
                      </div>
                      <span
                        hidden=""
                        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                      >
                        <input
                          hidden=""
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                        />
                        <input
                          hidden=""
                          name="Sunday-minutes"
                          readonly=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                          type="hidden"
                          value="0"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    minutes
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <div
                      class="flex"
                    >
                      <div
                        data-headlessui-state=""
                      >
                        <div
                          class="mr-2"
                        >
                          <label
                            class="text-md font-bold"
                            for="Sunday-active"
                            id="Sunday-active-label"
                          />
                        </div>
                        <button
                          aria-checked="false"
                          aria-label="Sunday-active"
                          class="bg-neutral/20 group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-info/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                          data-headlessui-state=""
                          id="Sunday-active"
                          role="switch"
                          tabindex="0"
                          type="button"
                        >
                          <span
                            aria-hidden="true"
                            class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                          />
                        </button>
                        <span
                          hidden=""
                          style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                        >
                          <input
                            hidden=""
                            readonly=""
                            style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            type="hidden"
                          />
                          <input
                            hidden=""
                            name="Sunday-active"
                            readonly=""
                            style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                            type="checkbox"
                            value="on"
                          />
                        </span>
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
      <div
        class="pb-4"
      />
      <button
        class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75"
        type="submit"
      >
        Save changes
      </button>
    </form>
  </div>
</body>
`;
