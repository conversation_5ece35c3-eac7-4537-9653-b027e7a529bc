// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`charger header should match snapshot with a single socket 1`] = `
<body>
  <div>
    <div
      class="lg:flex space-x-1 items-center"
    >
      <div
        class="flex"
      >
        <h1
          class="text-xxl font-bold"
        >
          Hello World
        </h1>
      </div>
    </div>
    <p
      class="text-md font-normal break-words"
    >
      I am cool
    </p>
  </div>
</body>
`;

exports[`charger header should match snapshot with multiple sockets 1`] = `
<body>
  <div>
    <div
      class="lg:flex items-center"
    >
      <div
        class="flex"
      >
        <h1
          class="text-xxl font-bold"
        >
          Hello World
        </h1>
      </div>
      <div
        class="lg:ml-6 my-4 lg:my-0"
      >
        <div
          class="flex space-x-4"
        >
          <button
            class="px-2 font-semibold border-b outline-hidden focus-visible:ring-2 focus-visible:ring-info text-info border-b-info hover:text-info cursor-default"
            disabled=""
          >
            Socket A
          </button>
          <button
            class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
          >
            Socket B
          </button>
        </div>
      </div>
    </div>
    <p
      class="text-md font-normal break-words"
    >
      I am cool
    </p>
  </div>
</body>
`;
