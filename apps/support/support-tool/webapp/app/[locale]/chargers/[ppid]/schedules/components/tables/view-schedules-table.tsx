import {
  Card,
  CheckmarkCircleIcon,
  CrossCircleIcon,
  Table,
} from '@experience/shared/react/design-system';
import { ChargeSchedules } from '@experience/support/support-tool/shared';
import { VerticalSpacer } from '@experience/shared/react/layouts';
import {
  days,
  getActiveStatusFromSchedule,
  getHoursFromSchedule,
  getMinutesFromSchedule,
  getStartTimesFromSchedule,
} from '../../utils/schedule-utils';
import { useTranslations } from 'next-intl';

export interface ViewSchedulesTableProps {
  schedule: ChargeSchedules[];
}

export const ViewSchedulesTable = ({ schedule }: ViewSchedulesTableProps) => {
  const t = useTranslations('Chargers.SchedulesEditPage');

  const formatDuration = (day: string): string => {
    const hours = getHoursFromSchedule(schedule)[day];
    const minutes = getMinutesFromSchedule(schedule)[day];
    return [
      hours > 0 ? hours.toString() : '',
      hours ? t('hours') : '',
      minutes > 0 ? minutes.toString() : '',
      minutes ? t('minutes') : '',
    ]
      .filter((result) => result !== '')
      .join(' ');
  };

  return (
    <>
      <VerticalSpacer />
      <Card>
        <Table caption="view-schedule-table">
          <Table.Header>
            <Table.Row>
              <Table.Heading>{t('day')}</Table.Heading>
              <Table.Heading>{t('start')}</Table.Heading>
              <Table.Heading>{t('duration')}</Table.Heading>
              <Table.Heading center>{t('active')}</Table.Heading>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {days.map((day) => (
              <Table.Row key={`${day}-row`}>
                <Table.Data>{t(day)}</Table.Data>
                <Table.Data>
                  {getStartTimesFromSchedule(schedule)[day]}
                </Table.Data>
                <Table.Data>{formatDuration(day)}</Table.Data>
                <Table.Data>
                  {getActiveStatusFromSchedule(schedule)[day] ? (
                    <CheckmarkCircleIcon.LIGHT
                      className="mx-auto text-primary"
                      height="h-6"
                      title={t('activeScheduleYes')}
                      width="w-6"
                    />
                  ) : (
                    <CrossCircleIcon.LIGHT
                      className="mx-auto text-error"
                      height="h-6"
                      title={t('activeScheduleNo')}
                      width="w-6"
                    />
                  )}
                </Table.Data>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      </Card>
    </>
  );
};
