'use client';

import { EvseSummary } from '@experience/shared/axios/assets-api-client';
import {
  TAB_STYLES_ACTIVE,
  TAB_STYLES_INACTIVE,
} from '@experience/shared/react/design-system';
import { twMerge } from 'tailwind-merge';
import { useRouter, useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';

export interface Socket {
  active: boolean;
  title: string;
  url: string;
}

export interface SocketSwitcherProps {
  sockets: Socket[];
}

interface UseSocketSwitcherProps {
  evses: EvseSummary[];
  evseId?: number;
  showActiveOption?: boolean;
  url: string;
}

export const useSocketSwitcher = ({
  evses,
  evseId = 1,
  url,
  showActiveOption = false,
}: UseSocketSwitcherProps): Socket[] => {
  const t = useTranslations('Chargers.Components.SocketSwitcher');
  const params = useSearchParams();

  const socketOptions = evses.map((_, index) => {
    const socket = String.fromCharCode(65 + index);
    return {
      active: params.get('socket') !== 'All' && index === evseId - 1,
      title: t('socketTitle', { socket }),
      url: `${url}?socket=${socket}`,
    };
  });

  if (showActiveOption) {
    return [
      {
        active: params.get('socket') === 'All',
        title: t('optionAll'),
        url: `${url}?socket=All`,
      },
      ...socketOptions,
    ];
  }

  return socketOptions;
};

export const SocketSwitcher = ({ sockets }: SocketSwitcherProps) => {
  const router = useRouter();

  const handleSocketClick = (url: string): void => {
    router.push(url);
  };

  return (
    <div className="flex space-x-4">
      {sockets.map(({ active, url, title }) => (
        <button
          key={url}
          disabled={active}
          onClick={() => handleSocketClick(url)}
          className={twMerge(
            `${TAB_STYLES_INACTIVE} focus-visible:ring-2 focus-visible:ring-info`,
            active ? TAB_STYLES_ACTIVE : ''
          )}
        >
          {title}
        </button>
      ))}
    </div>
  );
};

export default SocketSwitcher;
