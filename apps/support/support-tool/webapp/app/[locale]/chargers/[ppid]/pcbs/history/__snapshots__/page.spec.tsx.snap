// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`View pcb history page should match snapshot 1`] = `
<body>
  <div>
    <a
      class="text-info hover:underline hover:text-info active:text-info cursor-pointer flex items-center gap-1 mb-2 font-bold max-w-fit"
      href="/chargers/PSL-12345"
    >
      <svg
        class="fill-current w-4 h-4"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g
          fill="none"
        >
          <path
            d="M9.14 4L1 12.15L9.6 20.75"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
          <path
            d="M22.88 12.13H1"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
        </g>
      </svg>
      Back
    </a>
    <section
      class="p-3 rounded-lg bg-white"
    >
      <header>
        <div
          class="flex"
        >
          <div
            class="lg:flex items-center"
          >
            <div
              class="flex"
            >
              <h1
                class="text-xxl font-bold"
              >
                PCB history - PSL-12345 (Socket A)
              </h1>
            </div>
            <div
              class="lg:ml-6 my-4 lg:my-0"
            >
              <div
                class="flex space-x-4"
              >
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  All
                </button>
                <button
                  class="px-2 font-semibold border-b outline-hidden focus-visible:ring-2 focus-visible:ring-info text-info border-b-info hover:text-info cursor-default"
                  disabled=""
                >
                  Socket A
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket B
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket C
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket D
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket E
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket F
                </button>
              </div>
            </div>
          </div>
          <div
            class="ml-auto"
          />
        </div>
      </header>
    </section>
    <div
      class="pb-4"
    />
    <div
      class="flex"
    >
      <div
        class="flex items-center mb-4 space-x-4"
      >
        <p
          class="text-md font-normal py-2 self-end break-words"
        >
          Filters: 
        </p>
        <div
          class="w-36"
        >
          <div
            data-headlessui-state=""
          >
            <label
              aria-labelledby="status"
              class="block mb-2 text-md font-bold"
              data-headlessui-state=""
              for="status"
              id="headlessui-label-:test-id-1"
            >
              Status
            </label>
            <div
              class="relative mt-1"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-labelledby="headlessui-label-:test-id-1 status"
                class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-info/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-white"
                data-headlessui-state=""
                id="status"
                type="button"
              >
                <span
                  class="text-neutral block truncate"
                >
                  All
                </span>
                <span
                  class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                >
                  <svg
                    aria-hidden="true"
                    class="fill-current w-4 h-2.5 text-neutral"
                    viewBox="0 0 32 32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <title>
                      drop down
                    </title>
                    <g>
                      <path
                        d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                      />
                    </g>
                  </svg>
                </span>
              </button>
            </div>
            <span
              hidden=""
              style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
            />
          </div>
        </div>
        <button
          class="border-2 border-solid rounded-lg font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-info hover:underline hover:text-info active:font-bold active:text-info disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent py-1.5 px-3.75 self-end"
          type="button"
        >
          Clear
        </button>
      </div>
    </div>
    <div
      class="flex flex-col bg-white rounded-lg overflow-hidden overflow-x-auto"
    >
      <div
        class="inline-block min-w-full -my-2 py-2 align-middle"
      >
        <table
          class="min-w-full"
          tabindex="0"
        >
          <caption
            class="sr-only"
          >
            Table of pcb history
          </caption>
          <thead
            class="border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Date/Time
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Serial number
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Status
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
              <th
                class="text-left p-3 align-top"
                scope="col"
              >
                <button
                  class="flex items-center hover:underline text-left cursor-pointer"
                  type="button"
                >
                  Error code
                  <span
                    class="w-4 h-4 ml-1 text-neutral shrink-0"
                  />
                </button>
              </th>
            </tr>
          </thead>
          <tbody
            class="divide-y border-b border-b-neutral/20"
          >
            <tr
              class="border-b-neutral/20"
            >
              <td
                class="whitespace-normal px-3 py-4"
              >
                2021-07-01 12:00:00
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                12345678
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                Success
              </td>
              <td
                class="whitespace-normal px-3 py-4"
              >
                EAP0006
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</body>
`;
