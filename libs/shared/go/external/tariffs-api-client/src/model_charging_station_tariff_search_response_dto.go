/*
Tariffs Api

API for managing tariffs api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package tariffsapiclient

import (
	"bytes"
	"encoding/json"
	"fmt"
)

// checks if the ChargingStationTariffSearchResponseDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &ChargingStationTariffSearchResponseDto{}

// ChargingStationTariffSearchResponseDto struct for ChargingStationTariffSearchResponseDto
type ChargingStationTariffSearchResponseDto struct {
	// The tariffs matching the given criteria
	Data     []PersistedTariffRowDto                `json:"data"`
	Metadata ChargingStationTariffSearchMetadataDto `json:"metadata"`
}

type _ChargingStationTariffSearchResponseDto ChargingStationTariffSearchResponseDto

// NewChargingStationTariffSearchResponseDto instantiates a new ChargingStationTariffSearchResponseDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewChargingStationTariffSearchResponseDto(data []PersistedTariffRowDto, metadata ChargingStationTariffSearchMetadataDto) *ChargingStationTariffSearchResponseDto {
	this := ChargingStationTariffSearchResponseDto{}
	this.Data = data
	this.Metadata = metadata
	return &this
}

// NewChargingStationTariffSearchResponseDtoWithDefaults instantiates a new ChargingStationTariffSearchResponseDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewChargingStationTariffSearchResponseDtoWithDefaults() *ChargingStationTariffSearchResponseDto {
	this := ChargingStationTariffSearchResponseDto{}
	return &this
}

// GetData returns the Data field value
func (o *ChargingStationTariffSearchResponseDto) GetData() []PersistedTariffRowDto {
	if o == nil {
		var ret []PersistedTariffRowDto
		return ret
	}

	return o.Data
}

// GetDataOk returns a tuple with the Data field value
// and a boolean to check if the value has been set.
func (o *ChargingStationTariffSearchResponseDto) GetDataOk() ([]PersistedTariffRowDto, bool) {
	if o == nil {
		return nil, false
	}
	return o.Data, true
}

// SetData sets field value
func (o *ChargingStationTariffSearchResponseDto) SetData(v []PersistedTariffRowDto) {
	o.Data = v
}

// GetMetadata returns the Metadata field value
func (o *ChargingStationTariffSearchResponseDto) GetMetadata() ChargingStationTariffSearchMetadataDto {
	if o == nil {
		var ret ChargingStationTariffSearchMetadataDto
		return ret
	}

	return o.Metadata
}

// GetMetadataOk returns a tuple with the Metadata field value
// and a boolean to check if the value has been set.
func (o *ChargingStationTariffSearchResponseDto) GetMetadataOk() (*ChargingStationTariffSearchMetadataDto, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Metadata, true
}

// SetMetadata sets field value
func (o *ChargingStationTariffSearchResponseDto) SetMetadata(v ChargingStationTariffSearchMetadataDto) {
	o.Metadata = v
}

func (o ChargingStationTariffSearchResponseDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o ChargingStationTariffSearchResponseDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["data"] = o.Data
	toSerialize["metadata"] = o.Metadata
	return toSerialize, nil
}

func (o *ChargingStationTariffSearchResponseDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"data",
		"metadata",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varChargingStationTariffSearchResponseDto := _ChargingStationTariffSearchResponseDto{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varChargingStationTariffSearchResponseDto)

	if err != nil {
		return err
	}

	*o = ChargingStationTariffSearchResponseDto(varChargingStationTariffSearchResponseDto)

	return err
}

type NullableChargingStationTariffSearchResponseDto struct {
	value *ChargingStationTariffSearchResponseDto
	isSet bool
}

func (v NullableChargingStationTariffSearchResponseDto) Get() *ChargingStationTariffSearchResponseDto {
	return v.value
}

func (v *NullableChargingStationTariffSearchResponseDto) Set(val *ChargingStationTariffSearchResponseDto) {
	v.value = val
	v.isSet = true
}

func (v NullableChargingStationTariffSearchResponseDto) IsSet() bool {
	return v.isSet
}

func (v *NullableChargingStationTariffSearchResponseDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableChargingStationTariffSearchResponseDto(val *ChargingStationTariffSearchResponseDto) *NullableChargingStationTariffSearchResponseDto {
	return &NullableChargingStationTariffSearchResponseDto{value: val, isSet: true}
}

func (v NullableChargingStationTariffSearchResponseDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableChargingStationTariffSearchResponseDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
