import { InputBackgroundColour, InputWidth } from '../input/input';
import {
  fireEvent,
  render,
  screen,
  waitFor,
  within,
} from '@testing-library/react';
import Select, { Option } from './select';
import userEvent from '@testing-library/user-event';

const mockSetEnabled = jest.fn();
const mockOptions: Option[] = [
  { id: 1, name: 'Item one' },
  { id: 2, name: 'Item two' },
  { id: 3, name: 'Item three', disabled: true },
  { id: 4, name: 'Item four' },
];

const defaultProps = {
  id: 'select-field',
  label: 'Select field',
  name: 'selectField',
  options: mockOptions,
  placeholder: 'This is a placeholder',
  selected: mockOptions[0],
  onChange: mockSetEnabled,
};

describe('Select', () => {
  it('should render successfully', () => {
    const { baseElement } = render(<Select {...defaultProps} />);

    expect(baseElement).toBeTruthy();
  });

  it('should render a label and hint text ', () => {
    render(<Select {...defaultProps} hint="This is a selection hint" />);

    expect(screen.getByText('Select field')).toBeTruthy();
    expect(screen.getByText('This is a selection hint')).toBeTruthy();
  });

  it('should render an error message when an error occurs', () => {
    render(<Select {...defaultProps} errorMessage="There has been an error" />);

    expect(screen.getByText('There has been an error')).toBeTruthy();
  });

  it('should render error styling if error state is true', () => {
    render(<Select {...defaultProps} errorState={true} />);

    expect(screen.getByRole('button')).toHaveClass(
      'border-2 border-solid border-error'
    );
  });

  it('should render a placeholder if no option is selected', () => {
    render(
      <Select
        {...defaultProps}
        selected={undefined}
        placeholder="This is a placeholder"
      />
    );

    expect(screen.getByText('This is a placeholder')).toBeTruthy();
  });

  it('should show the name of the selected item in the dropdown button', () => {
    render(<Select {...defaultProps} />);

    expect(
      screen.getByRole('button', { name: 'Select field' })
    ).toHaveTextContent('Item one');
  });

  it('should show a dropdown when dropdown button is clicked', () => {
    render(<Select {...defaultProps} />);

    expect(screen.queryByRole('listbox')).not.toBeInTheDocument();

    const dropdownButton = screen.getByRole('button', {
      name: 'Select field',
    });
    fireEvent.click(dropdownButton);

    expect(screen.getByRole('listbox')).toBeInTheDocument();
  });

  it('should render option as disabled if prop is passed', async () => {
    render(<Select {...defaultProps} />);

    const dropdownButton = screen.getByRole('button', {
      name: 'Select field',
    });
    fireEvent.click(dropdownButton);

    expect(screen.getByRole('option', { name: 'Item three' })).toHaveAttribute(
      'aria-disabled',
      'true'
    );
  });

  it('should show name in list', () => {
    render(<Select {...defaultProps} />);

    expect(screen.queryByText(/item two/i)).not.toBeInTheDocument();

    const dropdownButton = screen.getByRole('button', {
      name: 'Select field',
    });
    fireEvent.click(dropdownButton);

    expect(screen.getByText(/item two/i)).toBeInTheDocument();
  });

  it('should show 4 items in the dropdown list', () => {
    render(<Select {...defaultProps} />);

    const dropdownButton = screen.getByRole('button', {
      name: 'Select field',
    });
    fireEvent.click(dropdownButton);

    const listboxElement = within(screen.getByRole('listbox'));

    expect(listboxElement.getAllByText(/item/i)).toHaveLength(4);
  });

  it('should close listbox when item is selected', async () => {
    render(<Select {...defaultProps} />);

    const dropdownButton = screen.getByRole('button', {
      name: 'Select field',
    });
    userEvent.click(dropdownButton);

    await waitFor(() => {
      const option = screen.getByRole('option', {
        name: /item two/i,
      });

      userEvent.click(option);
    });

    await waitFor(() => {
      expect(screen.queryByRole('listbox')).not.toBeInTheDocument();
    });
  });

  it('should display a checkmark icon on selected item', async () => {
    render(<Select {...defaultProps} selected={mockOptions[2]} />);

    const dropdownButton = screen.getByRole('button', {
      name: 'Select field',
    });
    fireEvent.click(dropdownButton);

    const option = screen.getByRole('option', { name: /item three/i });

    expect(within(option).getByTitle(/checkmark/i)).toBeTruthy();
  });

  it('should autofocus the select button if isFocused is true', async () => {
    render(<Select {...defaultProps} isFocused={true} />);

    expect(screen.getByRole('button')).toHaveFocus();
  });

  it('should match the snapshot', () => {
    const { baseElement } = render(<Select {...defaultProps} />);
    expect(baseElement).toMatchSnapshot();
  });

  it('should match the snapshot when dropdown is shown', () => {
    const { baseElement } = render(<Select {...defaultProps} />);

    const dropdownButton = screen.getByRole('button', {
      name: 'Select field',
    });
    fireEvent.click(dropdownButton);

    expect(baseElement).toMatchSnapshot();
  });

  it('should match the snapshot when an error occurs', () => {
    const { baseElement } = render(
      <Select {...defaultProps} errorMessage="There has been an error" />
    );

    expect(baseElement).toMatchSnapshot();
  });

  it('should match snapshot when anchor positioning props are passed', async () => {
    // we can set a value for the ListBoxButton ref offsetWidth - it defaults to 0 in JSDOM tests
    Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {
      configurable: true,
      value: 25,
    });

    const { baseElement } = render(
      <Select {...defaultProps} anchor={{ to: 'top' }} />
    );

    const dropdownButton = screen.getByRole('button', {
      name: 'Select field',
    });
    fireEvent.click(dropdownButton);

    expect(baseElement).toMatchSnapshot();

    // return it back to its original value
    Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {
      configurable: true,
      value: 0,
    });
  });

  it.each([
    InputWidth.FULL,
    InputWidth.HALF,
    InputWidth.ONE_THIRD,
    InputWidth.TWO_THIRDS,
  ])('should match the snapshot when input width is %s', (width) => {
    const { baseElement } = render(<Select {...defaultProps} width={width} />);
    expect(baseElement).toMatchSnapshot();
  });

  it.each([InputBackgroundColour.GREY, InputBackgroundColour.WHITE])(
    'should match the snapshot when background colour is %s',
    (colour) => {
      const { baseElement } = render(
        <Select {...defaultProps} backgroundColour={colour} />
      );
      expect(baseElement).toMatchSnapshot();
    }
  );

  it.each([true, false])('should allow boolean ids', (id) => {
    const booleanOptions = [
      { id, name: 'option one' },
      { id: !id, name: 'option two' },
    ];
    render(
      <Select
        {...defaultProps}
        options={booleanOptions}
        selected={booleanOptions[0]}
      />
    );

    const dropdownButton = screen.getByRole('button', {
      name: 'Select field',
    });
    fireEvent.click(dropdownButton);

    const option = screen.getByRole('option', {
      name: /option one/i,
    });

    fireEvent.click(option);
    expect(
      screen.getByRole('button', { name: 'Select field' })
    ).toHaveTextContent('option one');
  });
});
