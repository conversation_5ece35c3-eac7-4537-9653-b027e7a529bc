'use client';

import { AnchorPropsWithSelection } from '@headlessui/react/dist/internal/floating';
import { ArrowHeadDownIcon, CheckmarkIcon } from '../icons';
import {
  Field,
  Label,
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from '@headlessui/react';
import { InputBackgroundColour, InputWidth } from '../input/input';
import { useEffect, useRef, useState } from 'react';
import Paragraph, { TextSize, TextWeight } from '../paragraph/paragraph';
import classNames from 'classnames';

interface SelectProps {
  anchor?: AnchorPropsWithSelection;
  backgroundColour?: InputBackgroundColour;
  disabled?: boolean;
  errorMessage?: string;
  errorState?: boolean;
  hint?: string;
  id: string;
  isFocused?: boolean;
  label: string;
  name: string;
  options: Option[];
  placeholder: string;
  selected?: Option;
  onChange: (value: number | string) => void;
  srOnlyLabel?: boolean;
  width?: InputWidth;
}

export interface Option {
  id: number | string | boolean;
  name: string;
  disabled?: boolean;
}

export const Select = ({
  anchor,
  backgroundColour = InputBackgroundColour.GREY,
  disabled,
  errorMessage,
  errorState,
  hint,
  id,
  isFocused = false,
  label,
  name,
  options,
  placeholder,
  selected,
  onChange,
  srOnlyLabel,
  width,
}: SelectProps) => {
  const select = useRef<HTMLButtonElement>(null);
  const observer = useRef<ResizeObserver | null>(null);

  const [optionsWidth, setOptionsWidth] = useState<number | undefined>();

  const isErrorState = (): boolean => errorState || !!errorMessage;

  useEffect(() => {
    if (isFocused && select.current) {
      select.current.focus();
    }
  }, [isFocused]);

  // We use this to set the width of the ListBoxOptions to match the width of the ListBoxButton
  // when we are using the HeadlessUI anchor prop to position the ListBoxOptions
  useEffect(() => {
    const setOptionsWidthToReferenceWidth = (): void => {
      setOptionsWidth(select.current?.offsetWidth);
    };

    if (anchor && select.current) {
      setOptionsWidthToReferenceWidth();
      observer.current = new ResizeObserver(setOptionsWidthToReferenceWidth);
      observer.current.observe(select.current);
    }

    return () => {
      observer.current?.disconnect();
    };
  }, [anchor]);

  const selectField = (
    <Field>
      <Label
        className={classNames(
          'block mb-2',
          [{ 'sr-only': srOnlyLabel }],
          TextSize.Body,
          TextWeight.Bold
        )}
        htmlFor={id}
        aria-labelledby={id}
      >
        {label}
      </Label>
      <Listbox
        disabled={disabled}
        value={selected?.id ?? null}
        onChange={onChange}
        name={name}
      >
        <div className="relative mt-1">
          <ListboxButton
            className={classNames(
              [isErrorState() ? 'border-2 border-solid border-error' : null],
              'relative w-full',
              'focus:outline-hidden focus:ring-2 focus:ring-info/50',
              'text-left text-neutral',
              'disabled:bg-neutral/20 disabled:placeholder-neutral',
              'py-2 pl-1.5 pr-10 rounded-sm',
              backgroundColour,
              width
            )}
            id={id}
            ref={select}
          >
            <span
              className={classNames(
                { 'text-neutral': !selected?.name },
                'block truncate'
              )}
            >
              {selected?.name || placeholder}
            </span>
            <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
              <ArrowHeadDownIcon.SOLID
                className="w-4 h-2.5 text-neutral"
                aria-hidden="true"
                title="drop down"
              />
            </span>
          </ListboxButton>

          <ListboxOptions
            anchor={anchor}
            className={classNames(
              'absolute z-10 max-h-60 overflow-auto rounded-md bg-white mt-1 text-base transition duration-100 ease-in data-[leave]:opacity-0 data-[enter]:opacity-100 shadow-lg ring-1 ring-black/5 focus:outline-hidden',
              anchor ? '[--anchor-max-height:15rem]' : ''
            )}
            style={{ width: optionsWidth ? `${optionsWidth}px` : '100%' }}
          >
            {options.map((option) => (
              <ListboxOption
                key={option.name}
                disabled={option.disabled}
                className={({ focus }) =>
                  classNames(
                    { 'bg-neutral/10': focus },
                    'relative cursor-pointer select-none py-2 pl-1.5 pr-9',
                    option.disabled ? 'bg-neutral/20 pointer-events-none' : ''
                  )
                }
                value={option.id}
              >
                <span
                  className={classNames(
                    'block truncate text-xs',
                    option.disabled ? 'placeholder-neutral' : ''
                  )}
                >
                  {option.name}
                </span>

                {selected?.id === option.id ? (
                  <span className="absolute inset-y-0 right-0 flex items-center pr-1.5">
                    <CheckmarkIcon.SOLID className="text-primary" />
                  </span>
                ) : null}
              </ListboxOption>
            ))}
          </ListboxOptions>
        </div>
      </Listbox>
    </Field>
  );

  return (
    <>
      {selectField}

      {hint ? (
        <Paragraph
          textSize={TextSize.ExtraSmall}
          textWeight={TextWeight.Regular}
          className="text-neutral"
        >
          {hint}
        </Paragraph>
      ) : null}

      {errorMessage ? (
        <Paragraph
          textSize={TextSize.ExtraSmall}
          textWeight={TextWeight.Regular}
          className="text-error"
        >
          {errorMessage}
        </Paragraph>
      ) : null}
    </>
  );
};

export default Select;
