import { render, screen } from '@testing-library/react';
import Spinner from './spinner';

describe('Spinner', () => {
  it('should render successfully', () => {
    const { baseElement } = render(<Spinner />);
    expect(baseElement).toBeTruthy();
  });

  it('should match the snapshot', () => {
    const { baseElement } = render(<Spinner />);
    expect(baseElement).toMatchSnapshot();
  });

  it('should match the snapshot when a title is provided', () => {
    const { baseElement } = render(<Spinner title="Waiting..." />);
    expect(baseElement).toMatchSnapshot();
  });

  it('should contain the title text', () => {
    render(<Spinner title="Waiting..." />);
    expect(screen.getByRole('status', { name: 'Waiting...' })).toBeTruthy();
  });

  it.each([[true, false]])(
    'should have an element identifiable by the role status if it is an icon or not',
    (asIcon: boolean) => {
      render(<Spinner asIcon={asIcon} />);

      expect(screen.getByRole('status')).toBeTruthy();
    }
  );

  it('backgroundClassName should default to text-neutral/30', () => {
    render(<Spinner />);

    expect(screen.getByRole('status').children[0]).toHaveClass(
      'text-neutral/30'
    );
  });

  it('backgroundClassName should be applied if passed', () => {
    render(<Spinner backgroundClassName="text-neutral" />);

    expect(screen.getByRole('status').children[0]).toHaveClass('text-neutral');
  });

  it('spinnerClassName should be applied if passed', () => {
    render(<Spinner spinnerClassName="fill-primary" />);

    expect(screen.getByRole('status').children[0]).toHaveClass('fill-primary');
  });
});
