'use client';

import { ArrowDownIcon, ArrowUpIcon } from '../icons';
import { Table } from '../table/table';
import {
  UseAdvancedTableProps,
  useAdvancedTable,
} from './hooks/use-advanced-table';
import { flexRender } from '@tanstack/react-table';
import { twMerge } from 'tailwind-merge';
import Filters from './filters/filters';
import classNames from 'classnames';

export interface MetaType {
  cellClassName?: string;
  headerCellClassName?: string;
  sortButtonClassName?: string;
}

export type AdvancedTableProps<T> = UseAdvancedTableProps<T> & {
  clearFilters?: () => void;
  highlightAlternativeRows?: boolean;
};

export const AdvancedTable = <T,>({
  columns,
  data,
  caption,
  clearFilters,
  filters,
  hiddenColumns,
  id,
  initialSort = [],
  manualFiltering = false,
  meta,
  showSearchField = false,
  serverSideResultsCount,
  highlightAlternativeRows = false,
}: AdvancedTableProps<T>) => {
  const { globalFilter, results, table } = useAdvancedTable({
    caption,
    columns,
    data,
    filters,
    hiddenColumns,
    id,
    initialSort,
    meta,
    showSearchField,
    manualFiltering,
    serverSideResultsCount,
  });

  return (
    <>
      {filters || showSearchField ? (
        <div className="flex">
          <Filters
            clearFilters={clearFilters}
            filters={filters ?? {}}
            columns={table.getAllColumns()}
            globalFilter={globalFilter}
            showSearchField={showSearchField}
            setGlobalFilter={table.setGlobalFilter}
          />
          {results}
        </div>
      ) : null}
      <Table caption={caption}>
        <Table.Header>
          <Table.Row>
            {table.getHeaderGroups().map((headerGroup) =>
              headerGroup.headers.map((header) => (
                <Table.Heading
                  key={header.id}
                  aria-sort={header.column.getIsSorted() ?? 'none'}
                  className={twMerge(
                    classNames(
                      'align-top',
                      (header.column.columnDef.meta as MetaType)
                        ?.headerCellClassName
                    )
                  )}
                >
                  {header.column.getCanSort() ? (
                    <button
                      {...{
                        type: 'button',
                        className: twMerge(
                          classNames(
                            'flex items-center hover:underline text-left cursor-pointer',
                            (header.column.columnDef.meta as MetaType)
                              ?.sortButtonClassName
                          )
                        ),
                        onClick: header.column.getToggleSortingHandler(),
                      }}
                    >
                      {flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                      <span className="w-4 h-4 ml-1 text-neutral shrink-0">
                        {{
                          asc: (
                            <ArrowUpIcon.SOLID className="stroke-current stroke-2" />
                          ),
                          desc: (
                            <ArrowDownIcon.SOLID className="stroke-current stroke-2" />
                          ),
                        }[header.column.getIsSorted() as string] ?? null}
                      </span>
                    </button>
                  ) : (
                    flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )
                  )}
                </Table.Heading>
              ))
            )}
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {table.getRowModel().rows.map((row) => (
            <Table.Row
              key={row.id}
              className={classNames({
                'odd:bg-neutral/10': highlightAlternativeRows,
              })}
            >
              {row.getVisibleCells().map((cell) => (
                <Table.Data
                  key={cell.id}
                  className={
                    (cell.column.columnDef.meta as MetaType)?.cellClassName
                  }
                >
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </Table.Data>
              ))}
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
    </>
  );
};
