'use client';

import { InfoIcon } from '../icons';
import { twMerge } from 'tailwind-merge';
import Label from '../label/label';
import Paragraph, { TextSize, TextWeight } from '../paragraph/paragraph';
import React, { useEffect, useRef } from 'react';
import Tooltip from '../tooltip/tooltip';
import classNames from 'classnames';

export enum InputWidth {
  FULL = 'w-full',
  HALF = 'w-1/2',
  ONE_THIRD = 'w-1/3',
  TWO_THIRDS = 'w-2/3',
}

export enum InputBackgroundColour {
  GREY = 'bg-neutral/10',
  WHITE = 'bg-white',
}

export interface InputProps extends React.HTMLProps<HTMLInputElement> {
  backgroundColour?: InputBackgroundColour;
  disabled?: boolean;
  errorMessage?: string;
  hint?: string;
  icon?: React.JSX.Element;
  id: string;
  isFocused?: boolean;
  label: string;
  placeholder?: string;
  srOnlyLabel?: boolean;
  tooltip?: string;
  width?: InputWidth;
}

export const Input = ({
  backgroundColour = InputBackgroundColour.GREY,
  className,
  disabled,
  errorMessage,
  hint,
  icon,
  id,
  isFocused = false,
  srOnlyLabel = false,
  label,
  name,
  placeholder,
  tooltip,
  type,
  value,
  width,
  ...props
}: InputProps) => {
  const input = useRef<HTMLInputElement>(null);

  const isErrorState = (): boolean => !!errorMessage;

  const hasIcon = (): boolean => !!icon;

  useEffect(() => {
    if (isFocused && input.current) {
      input.current.focus();
    }
  }, [isFocused]);

  const inputField = (
    <input
      className={twMerge(
        classNames(
          [
            isErrorState()
              ? 'border-2 border-solid border-error'
              : 'border-none',
          ],
          [hasIcon() ? 'pl-8' : 'pl-1.5'],
          [
            'focus:outline-hidden focus:ring-2 focus:ring-info/50',
            'text-neutral placeholder-neutral',
            'disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed',
            'py-2 rounded-sm',
            backgroundColour,
            width,
          ],
          className
        )
      )}
      disabled={disabled}
      id={id}
      placeholder={placeholder}
      name={name}
      ref={input}
      type={type}
      value={value}
      {...props}
    />
  );

  return (
    <>
      <div className={classNames({ flex: tooltip })}>
        <Label
          className="block mb-2"
          htmlFor={id}
          text={label}
          isSrOnly={srOnlyLabel}
        />
        {tooltip ? (
          <Tooltip message={tooltip}>
            <InfoIcon.LIGHT className="ml-0.5" />
          </Tooltip>
        ) : null}
      </div>

      {icon ? (
        <div className="relative">
          <span className="inline-flex items-center space-x-4 absolute inset-y-2 px-2 text-neutral">
            {icon}
          </span>
          {inputField}
        </div>
      ) : null}

      {!icon && inputField}

      {hint ? (
        <Paragraph
          textSize={TextSize.ExtraSmall}
          textWeight={TextWeight.Regular}
          className="text-neutral"
        >
          {hint}
        </Paragraph>
      ) : null}

      {errorMessage ? (
        <Paragraph
          textSize={TextSize.ExtraSmall}
          textWeight={TextWeight.Regular}
          className="text-error"
        >
          {errorMessage}
        </Paragraph>
      ) : null}
    </>
  );
};

export default Input;
