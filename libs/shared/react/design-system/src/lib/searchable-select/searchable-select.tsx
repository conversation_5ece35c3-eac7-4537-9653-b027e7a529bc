'use client';

import {
  Combobox,
  ComboboxInput,
  ComboboxOption,
  ComboboxOptions,
  Field,
  Label,
} from '@headlessui/react';
import { InputBackgroundColour, InputWidth } from '../input/input';
import { Option } from '../select/select';
import { SearchIcon } from '../icons';
import { sort } from 'fast-sort';
import { useEffect, useRef, useState } from 'react';
import CrossIcon from '../icons/cross-icon/cross-icon';
import Paragraph, { TextSize, TextWeight } from '../paragraph/paragraph';
import classNames from 'classnames';

export interface SearchableSelectProps {
  backgroundColour?: InputBackgroundColour;
  className?: string;
  disabled?: boolean;
  errorMessage?: string;
  handleClear?: () => void;
  hint?: string;
  id: string;
  isFocused?: boolean;
  label: string;
  name?: string;
  onChange: (value: Option) => void;
  options: Option[];
  placeholder?: string;
  selected?: Option;
  srOnlyLabel?: boolean;
  width?: InputWidth;
}

export const SearchableSelect = ({
  backgroundColour = InputBackgroundColour.GREY,
  className,
  disabled,
  errorMessage,
  handleClear,
  hint,
  id,
  isFocused = false,
  label,
  name,
  onChange,
  options,
  placeholder,
  selected,
  srOnlyLabel,
  width = InputWidth.FULL,
}: SearchableSelectProps) => {
  const input = useRef<HTMLInputElement>(null);

  const [searchQuery, setSearchQuery] = useState('');

  const isErrorState = (): boolean => !!errorMessage;

  useEffect(() => {
    if (isFocused && input.current) {
      input.current.focus();
    }
  }, [isFocused]);

  const filteredOptions =
    searchQuery === ''
      ? options
      : options.filter((option) =>
          option.name.toLowerCase().includes(searchQuery.toLowerCase())
        );

  const filteredAndSortedOptions = sort(filteredOptions).asc([
    (option) => option.name.toLowerCase(),
  ]);

  const searchableSelectField = (
    <Field>
      <Label
        className={classNames(
          'block mb-2',
          [{ 'sr-only': srOnlyLabel }],
          TextSize.Body,
          TextWeight.Bold
        )}
        htmlFor={id}
        aria-labelledby={id}
      >
        {label}
      </Label>
      <Combobox
        disabled={disabled}
        value={selected ?? null}
        onChange={onChange}
      >
        <div className="relative">
          <span className="inline-flex items-center space-x-4 absolute top-1/2 -translate-y-1/2 px-2 text-neutral">
            <SearchIcon.LIGHT height="h-4" width="w-4" />
          </span>
          <ComboboxInput
            autoComplete="off"
            className={classNames(
              [
                isErrorState()
                  ? 'border-2 border-solid border-error'
                  : 'border-none',
              ],
              [
                'focus:outline-hidden focus:ring-2 focus:ring-info/50',
                'text-neutral placeholder-neutral',
                'disabled:bg-neutral/20 disabled:placeholder-neutral',
                'py-2 px-8 rounded-sm',
                backgroundColour,
                width,
              ],
              className
            )}
            displayValue={(item) => (item as Option)?.name ?? ''}
            id={id}
            name={name}
            onChange={(event) => setSearchQuery(event.target.value)}
            placeholder={placeholder}
            ref={input}
          />
          {handleClear && selected ? (
            <button
              aria-label={`Clear selection ${name}`}
              className={classNames(
                [selected ? 'inline-flex' : 'invisible'],
                'w-6 h-6 absolute flex items-center justify-center right-2 top-1/2 -translate-y-1/2 rounded-full text-neutral hover:text-neutral/80 focus:text-neutral/80 focus:ring-2 focus:ring-neutral focus:outline-hidden'
              )}
              name={`Clear selection ${name}`}
              onClick={(event) => {
                event.preventDefault();

                setSearchQuery('');

                if (input?.current) {
                  input.current.value = '';
                }

                handleClear();
              }}
            >
              <CrossIcon.LIGHT height="h-3" width="w-3" />
            </button>
          ) : null}
          <ComboboxOptions
            className={classNames([
              width,
              'absolute z-10 max-h-60 overflow-auto rounded-md bg-white mt-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-hidden',
            ])}
          >
            {filteredAndSortedOptions.map((option) => (
              <ComboboxOption
                data-attribute="option"
                key={option.name}
                className={({ active }) =>
                  classNames(
                    { 'bg-neutral/10': active },
                    'py-2 pl-1.5 cursor-pointer select-none text-xs hover:bg-neutral/10'
                  )
                }
                value={option}
              >
                {option.name}
              </ComboboxOption>
            ))}
          </ComboboxOptions>
        </div>
      </Combobox>
    </Field>
  );

  return (
    <>
      {searchableSelectField}
      {hint ? (
        <Paragraph
          textSize={TextSize.ExtraSmall}
          textWeight={TextWeight.Regular}
          className="text-neutral"
        >
          {hint}
        </Paragraph>
      ) : null}
      {errorMessage ? (
        <Paragraph
          textSize={TextSize.ExtraSmall}
          textWeight={TextWeight.Regular}
          className="text-error"
        >
          {errorMessage}
        </Paragraph>
      ) : null}
    </>
  );
};

export default SearchableSelect;
