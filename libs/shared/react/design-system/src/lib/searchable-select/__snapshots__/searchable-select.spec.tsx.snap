// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SearchableSelect snapshots should match the snapshot 1`] = `
<body>
  <div>
    <div
      data-headlessui-state=""
    >
      <label
        aria-labelledby="select-search"
        class="block mb-2 text-md font-bold"
        data-headlessui-state=""
        for="select-search"
        id="headlessui-label-testing"
      >
        Select search
      </label>
      <div
        class="relative"
        data-headlessui-state=""
      >
        <span
          class="inline-flex items-center space-x-4 absolute top-1/2 -translate-y-1/2 px-2 text-neutral"
        >
          <svg
            class="fill-current h-4 w-4"
            viewBox="0 0 32 32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M30,28.91l-6.2-6.2a12.94,12.94,0,1,0-1.43,1.41l6.21,6.21a1,1,0,0,0,1.42,0A1,1,0,0,0,30,28.91ZM3,14.15a11,11,0,1,1,11,11A11,11,0,0,1,3,14.15Z"
              />
            </g>
          </svg>
        </span>
        <input
          aria-autocomplete="list"
          aria-expanded="false"
          autocomplete="off"
          class="border-none focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 px-8 rounded-sm bg-neutral/10 w-full"
          data-headlessui-state=""
          id="select-search"
          name="selectSearch"
          placeholder="This is a placeholder"
          role="combobox"
          type="text"
          value=""
        />
      </div>
      <span
        hidden=""
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
      />
    </div>
  </div>
</body>
`;

exports[`SearchableSelect snapshots should match the snapshot when an error occurs 1`] = `
<body>
  <div>
    <div
      data-headlessui-state=""
    >
      <label
        aria-labelledby="select-search"
        class="block mb-2 text-md font-bold"
        data-headlessui-state=""
        for="select-search"
        id="headlessui-label-testing"
      >
        Select search
      </label>
      <div
        class="relative"
        data-headlessui-state=""
      >
        <span
          class="inline-flex items-center space-x-4 absolute top-1/2 -translate-y-1/2 px-2 text-neutral"
        >
          <svg
            class="fill-current h-4 w-4"
            viewBox="0 0 32 32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M30,28.91l-6.2-6.2a12.94,12.94,0,1,0-1.43,1.41l6.21,6.21a1,1,0,0,0,1.42,0A1,1,0,0,0,30,28.91ZM3,14.15a11,11,0,1,1,11,11A11,11,0,0,1,3,14.15Z"
              />
            </g>
          </svg>
        </span>
        <input
          aria-autocomplete="list"
          aria-expanded="false"
          autocomplete="off"
          class="border-2 border-solid border-error focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 px-8 rounded-sm bg-neutral/10 w-full"
          data-headlessui-state=""
          id="select-search"
          name="selectSearch"
          placeholder="This is a placeholder"
          role="combobox"
          type="text"
          value=""
        />
      </div>
      <span
        hidden=""
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
      />
    </div>
    <p
      class="text-xs font-normal text-error break-words"
    >
      There has been an error
    </p>
  </div>
</body>
`;

exports[`SearchableSelect snapshots should match the snapshot when background colour is bg-neutral/10 1`] = `
<body>
  <div>
    <div
      data-headlessui-state=""
    >
      <label
        aria-labelledby="select-search"
        class="block mb-2 text-md font-bold"
        data-headlessui-state=""
        for="select-search"
        id="headlessui-label-testing"
      >
        Select search
      </label>
      <div
        class="relative"
        data-headlessui-state=""
      >
        <span
          class="inline-flex items-center space-x-4 absolute top-1/2 -translate-y-1/2 px-2 text-neutral"
        >
          <svg
            class="fill-current h-4 w-4"
            viewBox="0 0 32 32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M30,28.91l-6.2-6.2a12.94,12.94,0,1,0-1.43,1.41l6.21,6.21a1,1,0,0,0,1.42,0A1,1,0,0,0,30,28.91ZM3,14.15a11,11,0,1,1,11,11A11,11,0,0,1,3,14.15Z"
              />
            </g>
          </svg>
        </span>
        <input
          aria-autocomplete="list"
          aria-expanded="false"
          autocomplete="off"
          class="border-none focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 px-8 rounded-sm bg-neutral/10 w-full"
          data-headlessui-state=""
          id="select-search"
          name="selectSearch"
          placeholder="This is a placeholder"
          role="combobox"
          type="text"
          value=""
        />
      </div>
      <span
        hidden=""
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
      />
    </div>
  </div>
</body>
`;

exports[`SearchableSelect snapshots should match the snapshot when background colour is bg-white 1`] = `
<body>
  <div>
    <div
      data-headlessui-state=""
    >
      <label
        aria-labelledby="select-search"
        class="block mb-2 text-md font-bold"
        data-headlessui-state=""
        for="select-search"
        id="headlessui-label-testing"
      >
        Select search
      </label>
      <div
        class="relative"
        data-headlessui-state=""
      >
        <span
          class="inline-flex items-center space-x-4 absolute top-1/2 -translate-y-1/2 px-2 text-neutral"
        >
          <svg
            class="fill-current h-4 w-4"
            viewBox="0 0 32 32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M30,28.91l-6.2-6.2a12.94,12.94,0,1,0-1.43,1.41l6.21,6.21a1,1,0,0,0,1.42,0A1,1,0,0,0,30,28.91ZM3,14.15a11,11,0,1,1,11,11A11,11,0,0,1,3,14.15Z"
              />
            </g>
          </svg>
        </span>
        <input
          aria-autocomplete="list"
          aria-expanded="false"
          autocomplete="off"
          class="border-none focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 px-8 rounded-sm bg-white w-full"
          data-headlessui-state=""
          id="select-search"
          name="selectSearch"
          placeholder="This is a placeholder"
          role="combobox"
          type="text"
          value=""
        />
      </div>
      <span
        hidden=""
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
      />
    </div>
  </div>
</body>
`;

exports[`SearchableSelect snapshots should match the snapshot when dropdown is shown 1`] = `
<body>
  <div>
    <div
      data-headlessui-state=""
    >
      <label
        aria-hidden="true"
        aria-labelledby="select-search"
        class="block mb-2 text-md font-bold"
        data-headlessui-state=""
        for="select-search"
        id="headlessui-label-testing"
      >
        Select search
      </label>
      <div
        class="relative"
        data-headlessui-state="open"
        data-open=""
      >
        <span
          aria-hidden="true"
          class="inline-flex items-center space-x-4 absolute top-1/2 -translate-y-1/2 px-2 text-neutral"
        >
          <svg
            class="fill-current h-4 w-4"
            viewBox="0 0 32 32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M30,28.91l-6.2-6.2a12.94,12.94,0,1,0-1.43,1.41l6.21,6.21a1,1,0,0,0,1.42,0A1,1,0,0,0,30,28.91ZM3,14.15a11,11,0,1,1,11,11A11,11,0,0,1,3,14.15Z"
              />
            </g>
          </svg>
        </span>
        <input
          aria-activedescendant="headlessui-combobox-option-testing"
          aria-autocomplete="list"
          aria-controls="headlessui-combobox-options-testing"
          aria-expanded="true"
          autocomplete="off"
          class="border-none focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 px-8 rounded-sm bg-neutral/10 w-full"
          data-headlessui-state="open"
          data-open=""
          id="select-search"
          name="selectSearch"
          placeholder="This is a placeholder"
          role="combobox"
          type="text"
          value=""
        />
        <div
          aria-labelledby=""
          class="w-full absolute z-10 max-h-60 overflow-auto rounded-md bg-white mt-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-hidden"
          data-headlessui-state="open"
          data-open=""
          id="headlessui-combobox-options-testing"
          role="listbox"
          style="--input-width: 0px; --button-width: 0px;"
        >
          <div
            aria-selected="false"
            class="bg-neutral/10 py-2 pl-1.5 cursor-pointer select-none text-xs hover:bg-neutral/10"
            data-active=""
            data-attribute="option"
            data-focus=""
            data-headlessui-state="active focus"
            id="headlessui-combobox-option-testing"
            role="option"
            tabindex="-1"
          >
            Item four
          </div>
          <div
            aria-selected="true"
            class="bg-neutral/10 py-2 pl-1.5 cursor-pointer select-none text-xs hover:bg-neutral/10"
            data-active=""
            data-attribute="option"
            data-focus=""
            data-headlessui-state="active focus selected"
            data-selected=""
            id="headlessui-combobox-option-testing"
            role="option"
            tabindex="-1"
          >
            Item one
          </div>
          <div
            aria-selected="false"
            class="bg-neutral/10 py-2 pl-1.5 cursor-pointer select-none text-xs hover:bg-neutral/10"
            data-active=""
            data-attribute="option"
            data-focus=""
            data-headlessui-state="active focus"
            id="headlessui-combobox-option-testing"
            role="option"
            tabindex="-1"
          >
            Item three
          </div>
          <div
            aria-selected="false"
            class="bg-neutral/10 py-2 pl-1.5 cursor-pointer select-none text-xs hover:bg-neutral/10"
            data-active=""
            data-attribute="option"
            data-focus=""
            data-headlessui-state="active focus"
            id="headlessui-combobox-option-testing"
            role="option"
            tabindex="-1"
          >
            Item two
          </div>
        </div>
      </div>
      <span
        aria-hidden="true"
        hidden=""
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
      />
    </div>
  </div>
</body>
`;

exports[`SearchableSelect snapshots should match the snapshot when input is clicked on and dropdown is shown 1`] = `
<body>
  <div>
    <div
      data-headlessui-state=""
    >
      <label
        aria-labelledby="select-search"
        class="block mb-2 text-md font-bold"
        data-headlessui-state=""
        for="select-search"
        id="headlessui-label-testing"
      >
        Select search
      </label>
      <div
        class="relative"
        data-headlessui-state=""
      >
        <span
          class="inline-flex items-center space-x-4 absolute top-1/2 -translate-y-1/2 px-2 text-neutral"
        >
          <svg
            class="fill-current h-4 w-4"
            viewBox="0 0 32 32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M30,28.91l-6.2-6.2a12.94,12.94,0,1,0-1.43,1.41l6.21,6.21a1,1,0,0,0,1.42,0A1,1,0,0,0,30,28.91ZM3,14.15a11,11,0,1,1,11,11A11,11,0,0,1,3,14.15Z"
              />
            </g>
          </svg>
        </span>
        <input
          aria-autocomplete="list"
          aria-expanded="false"
          autocomplete="off"
          class="border-none focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 px-8 rounded-sm bg-neutral/10 w-full"
          data-headlessui-state=""
          id="select-search"
          name="selectSearch"
          placeholder="This is a placeholder"
          role="combobox"
          type="text"
          value=""
        />
      </div>
      <span
        hidden=""
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
      />
    </div>
  </div>
</body>
`;

exports[`SearchableSelect snapshots should match the snapshot when input width is w-1/2 1`] = `
<body>
  <div>
    <div
      data-headlessui-state=""
    >
      <label
        aria-labelledby="select-search"
        class="block mb-2 text-md font-bold"
        data-headlessui-state=""
        for="select-search"
        id="headlessui-label-testing"
      >
        Select search
      </label>
      <div
        class="relative"
        data-headlessui-state=""
      >
        <span
          class="inline-flex items-center space-x-4 absolute top-1/2 -translate-y-1/2 px-2 text-neutral"
        >
          <svg
            class="fill-current h-4 w-4"
            viewBox="0 0 32 32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M30,28.91l-6.2-6.2a12.94,12.94,0,1,0-1.43,1.41l6.21,6.21a1,1,0,0,0,1.42,0A1,1,0,0,0,30,28.91ZM3,14.15a11,11,0,1,1,11,11A11,11,0,0,1,3,14.15Z"
              />
            </g>
          </svg>
        </span>
        <input
          aria-autocomplete="list"
          aria-expanded="false"
          autocomplete="off"
          class="border-none focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 px-8 rounded-sm bg-neutral/10 w-1/2"
          data-headlessui-state=""
          id="select-search"
          name="selectSearch"
          placeholder="This is a placeholder"
          role="combobox"
          type="text"
          value=""
        />
      </div>
      <span
        hidden=""
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
      />
    </div>
  </div>
</body>
`;

exports[`SearchableSelect snapshots should match the snapshot when input width is w-1/3 1`] = `
<body>
  <div>
    <div
      data-headlessui-state=""
    >
      <label
        aria-labelledby="select-search"
        class="block mb-2 text-md font-bold"
        data-headlessui-state=""
        for="select-search"
        id="headlessui-label-testing"
      >
        Select search
      </label>
      <div
        class="relative"
        data-headlessui-state=""
      >
        <span
          class="inline-flex items-center space-x-4 absolute top-1/2 -translate-y-1/2 px-2 text-neutral"
        >
          <svg
            class="fill-current h-4 w-4"
            viewBox="0 0 32 32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M30,28.91l-6.2-6.2a12.94,12.94,0,1,0-1.43,1.41l6.21,6.21a1,1,0,0,0,1.42,0A1,1,0,0,0,30,28.91ZM3,14.15a11,11,0,1,1,11,11A11,11,0,0,1,3,14.15Z"
              />
            </g>
          </svg>
        </span>
        <input
          aria-autocomplete="list"
          aria-expanded="false"
          autocomplete="off"
          class="border-none focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 px-8 rounded-sm bg-neutral/10 w-1/3"
          data-headlessui-state=""
          id="select-search"
          name="selectSearch"
          placeholder="This is a placeholder"
          role="combobox"
          type="text"
          value=""
        />
      </div>
      <span
        hidden=""
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
      />
    </div>
  </div>
</body>
`;

exports[`SearchableSelect snapshots should match the snapshot when input width is w-2/3 1`] = `
<body>
  <div>
    <div
      data-headlessui-state=""
    >
      <label
        aria-labelledby="select-search"
        class="block mb-2 text-md font-bold"
        data-headlessui-state=""
        for="select-search"
        id="headlessui-label-testing"
      >
        Select search
      </label>
      <div
        class="relative"
        data-headlessui-state=""
      >
        <span
          class="inline-flex items-center space-x-4 absolute top-1/2 -translate-y-1/2 px-2 text-neutral"
        >
          <svg
            class="fill-current h-4 w-4"
            viewBox="0 0 32 32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M30,28.91l-6.2-6.2a12.94,12.94,0,1,0-1.43,1.41l6.21,6.21a1,1,0,0,0,1.42,0A1,1,0,0,0,30,28.91ZM3,14.15a11,11,0,1,1,11,11A11,11,0,0,1,3,14.15Z"
              />
            </g>
          </svg>
        </span>
        <input
          aria-autocomplete="list"
          aria-expanded="false"
          autocomplete="off"
          class="border-none focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 px-8 rounded-sm bg-neutral/10 w-2/3"
          data-headlessui-state=""
          id="select-search"
          name="selectSearch"
          placeholder="This is a placeholder"
          role="combobox"
          type="text"
          value=""
        />
      </div>
      <span
        hidden=""
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
      />
    </div>
  </div>
</body>
`;

exports[`SearchableSelect snapshots should match the snapshot when input width is w-full 1`] = `
<body>
  <div>
    <div
      data-headlessui-state=""
    >
      <label
        aria-labelledby="select-search"
        class="block mb-2 text-md font-bold"
        data-headlessui-state=""
        for="select-search"
        id="headlessui-label-testing"
      >
        Select search
      </label>
      <div
        class="relative"
        data-headlessui-state=""
      >
        <span
          class="inline-flex items-center space-x-4 absolute top-1/2 -translate-y-1/2 px-2 text-neutral"
        >
          <svg
            class="fill-current h-4 w-4"
            viewBox="0 0 32 32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M30,28.91l-6.2-6.2a12.94,12.94,0,1,0-1.43,1.41l6.21,6.21a1,1,0,0,0,1.42,0A1,1,0,0,0,30,28.91ZM3,14.15a11,11,0,1,1,11,11A11,11,0,0,1,3,14.15Z"
              />
            </g>
          </svg>
        </span>
        <input
          aria-autocomplete="list"
          aria-expanded="false"
          autocomplete="off"
          class="border-none focus:outline-hidden focus:ring-2 focus:ring-info/50 text-neutral placeholder-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 px-8 rounded-sm bg-neutral/10 w-full"
          data-headlessui-state=""
          id="select-search"
          name="selectSearch"
          placeholder="This is a placeholder"
          role="combobox"
          type="text"
          value=""
        />
      </div>
      <span
        hidden=""
        style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
      />
    </div>
  </div>
</body>
`;
