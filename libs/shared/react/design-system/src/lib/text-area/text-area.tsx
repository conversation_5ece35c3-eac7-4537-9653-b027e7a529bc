'use client';

import Paragraph, { TextSize, TextWeight } from '../paragraph/paragraph';
import React, { useEffect, useRef } from 'react';
import classNames from 'classnames';

export enum TextAreaWidth {
  FULL = 'w-full',
  HALF = 'w-1/2',
  ONE_THIRD = 'w-1/3',
  TWO_THIRDS = 'w-2/3',
}

export interface TextAreaProps extends React.HTMLProps<HTMLTextAreaElement> {
  disabled?: boolean;
  errorMessage?: string;
  height?: string;
  hint?: string;
  isFocused?: boolean;
  label?: string;
  placeholder?: string;
  srOnlyLabel?: boolean;
  width?: TextAreaWidth;
}

export const TextArea = ({
  className,
  disabled,
  errorMessage,
  height = 'h-36',
  hint,
  id,
  isFocused = false,
  srOnlyLabel = false,
  label,
  name,
  placeholder,
  type,
  value,
  width,
  ...props
}: TextAreaProps) => {
  const textArea = useRef<HTMLTextAreaElement>(null);

  const isErrorState = (): boolean => !!errorMessage;

  useEffect(() => {
    if (isFocused && textArea.current) {
      textArea.current.focus();
    }
  }, [isFocused]);

  const textAreaField = (
    <textarea
      className={classNames(
        [isErrorState() ? 'border-2 border-solid border-error' : 'border-none'],
        [
          'focus:outline-hidden focus:ring-2 focus:ring-info/50',
          'text-neutral placeholder-neutral',
          'disabled:bg-neutral/20 disabled:placeholder-neutral',
          'py-2 rounded-sm',
          'bg-neutral/20',
          height,
          width,
        ],
        className
      )}
      disabled={disabled}
      id={id}
      placeholder={placeholder}
      name={name}
      value={value}
      ref={textArea}
      {...props}
    />
  );

  return (
    <>
      <label
        htmlFor={id}
        aria-labelledby={id}
        className={classNames(
          'block mb-2',
          [{ 'sr-only': srOnlyLabel }],
          TextSize.Body,
          TextWeight.Bold
        )}
      >
        {label}
      </label>

      {textAreaField}

      {hint ? (
        <Paragraph
          textSize={TextSize.ExtraSmall}
          textWeight={TextWeight.Regular}
          className="text-neutral"
        >
          {hint}
        </Paragraph>
      ) : null}

      {errorMessage ? (
        <Paragraph
          textSize={TextSize.ExtraSmall}
          textWeight={TextWeight.Regular}
          className="text-error"
        >
          {errorMessage}
        </Paragraph>
      ) : null}
    </>
  );
};

export default TextArea;
