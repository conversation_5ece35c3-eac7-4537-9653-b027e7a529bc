'use client';

import CaretDownIcon from '../icons/caret-down-icon/caret-down-icon';
import CaretUpIcon from '../icons/caret-up-icon/caret-up-icon';
import React, { useEffect, useState } from 'react';

export interface AccordionProps {
  open?: boolean;
  heading: string;
  children: React.ReactNode;
}

export const Accordion = ({
  heading,
  children,
  open = false,
}: AccordionProps) => {
  const [isOpen, setIsOpen] = useState(open);

  useEffect(() => {
    setIsOpen(open);
  }, [open]);

  return (
    <>
      <button
        className="flex w-full items-start justify-between text-left text-charcoal"
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
      >
        <span className="font-medium leading-7 ml-4">{heading}</span>
        <span className="ml-6 mr-4 flex h-7 items-center">
          {isOpen ? <CaretUpIcon.SOLID /> : <CaretDownIcon.SOLID />}
        </span>
      </button>
      {isOpen ? <div className="mt-2 ml-4 pr-12">{children}</div> : null}
      <hr className="h-px mt-2 bg-neutral/40 border-0" />
    </>
  );
};

export default Accordion;
