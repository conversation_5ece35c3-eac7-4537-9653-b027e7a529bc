// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Accordion should match snapshot when state is closed 1`] = `
<body>
  <div>
    <button
      aria-expanded="false"
      class="flex w-full items-start justify-between text-left text-charcoal"
    >
      <span
        class="font-medium leading-7 ml-4"
      >
        Hello world!
      </span>
      <span
        class="ml-6 mr-4 flex h-7 items-center"
      >
        <svg
          class="fill-current h-4 w-4"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="m12,18.71c-.38,0-.77-.15-1.06-.44L.53,7.85C-.06,7.27-.06,6.32.53,5.73s1.54-.59,2.12,0l9.35,9.35,9.35-9.35c.59-.59,1.54-.59,2.12,0s.59,1.54,0,2.12l-10.41,10.41c-.29.29-.68.44-1.06.44Z"
          />
        </svg>
      </span>
    </button>
    <hr
      class="h-px mt-2 bg-neutral/40 border-0"
    />
  </div>
</body>
`;

exports[`Accordion should match snapshot when state is open 1`] = `
<body>
  <div>
    <button
      aria-expanded="true"
      class="flex w-full items-start justify-between text-left text-charcoal"
    >
      <span
        class="font-medium leading-7 ml-4"
      >
        Hello world!
      </span>
      <span
        class="ml-6 mr-4 flex h-7 items-center"
      >
        <svg
          class="fill-current h-4 w-4"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="m22.46,18.73c-.38,0-.77-.15-1.06-.44l-9.4-9.4L2.6,18.29c-.59.59-1.54.59-2.12,0s-.59-1.54,0-2.12L10.94,5.71c.59-.59,1.54-.59,2.12,0l10.46,10.46c.59.59.59,1.54,0,2.12-.29.29-.68.44-1.06.44Z"
          />
        </svg>
      </span>
    </button>
    <div
      class="mt-2 ml-4 pr-12"
    >
      <p
        class="text-md font-normal break-words"
      >
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl eget ultricies aliquam, nunc sapien ultricies nunc, quis ultricie
      </p>
    </div>
    <hr
      class="h-px mt-2 bg-neutral/40 border-0"
    />
  </div>
</body>
`;
