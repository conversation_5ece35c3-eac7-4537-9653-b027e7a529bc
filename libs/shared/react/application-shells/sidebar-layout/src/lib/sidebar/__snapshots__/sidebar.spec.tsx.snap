// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Sidebar should match snapshot when path is { name: 'Home', href: '/', icon: 'HomeIcon' } 1`] = `
<body>
  <div>
    <div
      class="flex-1 flex-col pt-5 pb-4"
    >
      <div
        class="items-center px-4"
      >
        <svg
          height="55px"
          viewBox="0 0 554.67 497.78"
          width="150px"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g
            style="isolation: isolate;"
          >
            <g
              id="Layer_1"
            >
              <path
                class="fill-current color-primary"
                d="M284.66,226.05h-14.61c-8.86-56.15-59.34-113.23-134.08-113.23S0,170.81,0,248.83v225.81c0,12.78,10.36,23.15,23.15,23.15h44.71c12.78,0,23.15-10.36,23.15-23.15v-97.48c17.09,5.84,34.47,8.4,52.17,7.4,12.83,69.39,71.26,113.22,134.08,113.22,72.7,0,124.96-55.22,134.05-113.02,79.86,3.89,143.37-59.02,143.37-135.8V23.15c0-12.78-10.36-23.15-23.15-23.15h-44.71c-12.78,0-23.15,10.36-23.15,23.15v97.48c-63.3-23.51-160.1,6.59-179.01,105.43ZM463.66,249.02c-.01,24.78-20.18,44.94-44.96,44.94s-44.96-20.17-44.96-44.96,20.17-44.96,44.96-44.96,44.94,20.15,44.96,44.93v.03s0,.01,0,.02ZM23.15,248.85s0,0,0,0v-.06h0s0-.05,0-.08c.04-62.27,50.54-112.74,112.82-112.74,55.62,0,101.84,40.26,111.12,93.22-15.31,3.45-29.84,9.53-43.05,18.01-.85-36.88-30.99-66.52-68.07-66.52s-68.11,30.49-68.11,68.11h0v.04s0,.03,0,.04c0,0,0,0,0,0h0v219.48c0,4.19-3.4,7.59-7.59,7.59h-29.53c-4.19,0-7.59-3.4-7.59-7.59v-219.49ZM91.01,248.76c.02-24.78,20.18-44.93,44.96-44.93s44.96,20.17,44.96,44.96-20.17,44.96-44.96,44.96-44.94-20.15-44.96-44.93v-.03s0-.02,0-.03ZM282.74,249.14c.02,15.89,2.74,31.37,7.96,45.9-26.51-4.9-49.65,5.06-64.2,21.38-34.38,44.6-91.23,55.42-135.49,35.87v-52.36c29.37,25.4,72.86,22.29,98.13-8.6,24.06-28.84,55.05-43.33,93.6-42.18ZM322.22,361.82c0,24.79-20.17,44.96-44.96,44.96s-44.96-20.17-44.96-44.96,20.17-44.96,44.96-44.96,44.96,20.17,44.96,44.96ZM307.6,229.31c16.84-80.82,95.69-109.42,156.07-83.81v52.36c-12-10.56-27.73-16.97-44.96-16.97-37.62,0-68.11,30.49-68.11,68.11,0,16.03,5.55,30.75,14.81,42.38h-.02c21.42,26.73,28.35,59.95,22.96,90.2-9.34,52.89-55.53,93.06-111.09,93.06s-101.84-40.26-111.12-93.22c15.31-3.45,29.84-9.53,43.05-18.01.85,36.88,30.99,66.52,68.07,66.52s68.11-30.49,68.11-68.11c.04-15.85-5.11-29.96-14.9-42.52-20.7-26.78-28.19-56.81-22.87-89.99ZM494.41,21.85h29.52c4.2,0,7.6,3.4,7.6,7.6v219.77c0,69.01-59.45,116.01-118.3,112.45-.02-15.89-2.74-31.37-7.96-45.9,41.14,8.52,81.55-22.74,81.55-66.74V29.44c0-4.2,3.4-7.6,7.6-7.6Z"
                style="mix-blend-mode: multiply;"
              />
            </g>
          </g>
        </svg>
      </div>
      <nav
        class="mt-5 flex-1 px-6 space-y-1"
      >
        <a
          class="text-info font-bold group flex items-center py-2 font-semibold tracking-wide"
          href="/"
        >
          <svg
            aria-hidden="true"
            class="fill-current h-6 text-info mr-3 shrink-0"
            viewBox="0 0 32 32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M26.17,30.11H7.31A4.09,4.09,0,0,1,3.22,26V13.28A4.1,4.1,0,0,1,4.84,10l9.42-7.15a4.11,4.11,0,0,1,5,0L28.65,10h0a4.12,4.12,0,0,1,1.61,3.26V26A4.1,4.1,0,0,1,26.17,30.11ZM16.74,4a2.13,2.13,0,0,0-1.27.42L6.05,11.61a2.1,2.1,0,0,0-.83,1.67V26a2.1,2.1,0,0,0,2.09,2.09H26.17A2.1,2.1,0,0,0,28.26,26V13.28a2.14,2.14,0,0,0-.82-1.67L18,4.46A2.1,2.1,0,0,0,16.74,4Zm4.47,19.77a4.38,4.38,0,1,1,4.38-4.37A4.37,4.37,0,0,1,21.21,23.81Zm0-6.75a2.38,2.38,0,1,0,2.38,2.38A2.38,2.38,0,0,0,21.21,17.06Z"
              />
            </g>
          </svg>
          Home
        </a>
        <a
          class="text-neutral hover:text-info hover:underline group flex items-center py-2 font-semibold tracking-wide"
          href="/info"
        >
          <svg
            aria-hidden="true"
            class="fill-current h-6 text-neutral group-hover:text-info mr-3 shrink-0"
            viewBox="0 0 32 32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
              />
              <path
                d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
              />
              <path
                d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
              />
            </g>
          </svg>
          Info
        </a>
      </nav>
    </div>
    <div
      class="px-6 py-4 space-y-1"
    >
      <a
        class="text-neutral hover:text-info hover:underline group flex items-center py-2 font-semibold tracking-wide"
        href="https://www.google.com"
        id="navigation-feedback"
        rel="noreferrer"
        target="_blank"
      >
        <svg
          aria-hidden="true"
          class="fill-current h-6 text-neutral group-hover:text-info mr-3 shrink-0"
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g>
            <path
              d="M27.62,5.55h-5.09v-1.78c0-2.08-1.69-3.78-3.78-3.78H3.78C1.69,0,0,1.69,0,3.78V13.75c0,1.97,1.51,3.59,3.44,3.76l-.03,2.09c0,.36,.19,.7,.51,.87,.14,.08,.3,.12,.46,.12,.19,0,.38-.06,.55-.17l3.93-2.69v1.57c0,2.08,1.69,3.78,3.78,3.78h9.6l4.23,2.89c.17,.11,.36,.17,.55,.17,.16,0,.31-.04,.46-.12,.32-.17,.52-.51,.51-.87l-.03-2.09c1.92-.17,3.44-1.79,3.44-3.76V9.33c0-2.08-1.69-3.78-3.78-3.78ZM8.31,15.75l-2.93,2.01v-1.19c.02-.26-.08-.51-.27-.69-.18-.18-.43-.29-.69-.29h-.65c-1.01,0-1.83-.82-1.83-1.83V3.78c0-1.01,.82-1.83,1.83-1.83h14.98c1.01,0,1.83,.82,1.83,1.83V13.75c0,1.01-.82,1.83-1.83,1.83H8.86c-.2,0-.39,.06-.55,.17Zm21.14,3.55c0,1.01-.82,1.83-1.83,1.83h-.65c-.26,0-.51,.1-.69,.29-.18,.18-.28,.43-.28,.69v1.19s-2.92-2.01-2.92-2.01c-.16-.11-.35-.17-.55-.17H12.64c-1.01,0-1.83-.82-1.83-1.83v-1.78h7.95c2.08,0,3.78-1.69,3.78-3.78V7.5h5.09c1.01,0,1.83,.82,1.83,1.83v9.97Z"
            />
            <path
              d="M5.09,7.2H14.09c.54,0,.97-.44,.97-.97s-.44-.97-.97-.97H5.09c-.54,0-.97,.44-.97,.97s.44,.97,.97,.97Z"
            />
            <path
              d="M9.11,10.23H5.09c-.54,0-.97,.44-.97,.97s.44,.97,.97,.97h4.02c.54,0,.97-.44,.97-.97s-.44-.97-.97-.97Z"
            />
          </g>
        </svg>
        Feedback
      </a>
      <a
        class="text-neutral hover:text-info hover:underline group flex items-center py-2 font-semibold tracking-wide"
        href="/profile"
      >
        <svg
          aria-hidden="true"
          class="fill-current h-6 text-neutral group-hover:text-info mr-3 shrink-0"
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g>
            <path
              d="M15.69,18A8.09,8.09,0,1,1,23.78,9.9,8.1,8.1,0,0,1,15.69,18Zm0-14.17A6.09,6.09,0,1,0,21.78,9.9,6.09,6.09,0,0,0,15.69,3.82Z"
            />
            <path
              d="M2.15,29.62a1.13,1.13,0,0,1-.32-.05A1,1,0,0,1,1.2,28.3c1.92-5.66,8-9.62,14.78-9.62s12.68,3.78,14.71,9.4a1,1,0,0,1-1.89.68c-1.74-4.84-6.89-8.08-12.82-8.08S4.74,24.08,3.1,28.94A1,1,0,0,1,2.15,29.62Z"
            />
          </g>
        </svg>
        Profile
      </a>
      <a
        class="text-neutral hover:text-info hover:underline group flex items-center py-2 font-semibold tracking-wide"
        href="/auth/logout"
      >
        <svg
          aria-hidden="true"
          class="fill-current h-6 text-neutral group-hover:text-info mr-3 shrink-0"
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g>
            <path
              d="M24.77,2.4H15a5,5,0,0,0-4.85,5v3.15a1,1,0,0,0,2,0V7.44A3,3,0,0,1,15,4.4h9.74a3,3,0,0,1,2.85,3V24.07a3,3,0,0,1-2.85,3H15a3,3,0,0,1-2.85-3V21.34a1,1,0,1,0-2,0v2.73a5,5,0,0,0,4.85,5h9.74a5,5,0,0,0,4.85-5V7.44A5,5,0,0,0,24.77,2.4Z"
            />
            <path
              d="M5.12,17H16.18a1,1,0,0,0,0-2H5.12L7.5,12.58a1,1,0,0,0,0-1.41,1,1,0,0,0-1.41,0L2,15.26a1,1,0,0,0-.21.32,1,1,0,0,0,0,.77,1,1,0,0,0,.21.32l4.1,4.1A1,1,0,1,0,7.5,19.35Z"
            />
          </g>
        </svg>
        Log out
      </a>
    </div>
    <p
      class="text-xs text-neutral/60 hover:text-info hover:underline ml-1"
    />
  </div>
</body>
`;

exports[`Sidebar should match snapshot when path is { name: 'Info', href: '/info', icon: 'InfoIcon' } 1`] = `
<body>
  <div>
    <div
      class="flex-1 flex-col pt-5 pb-4"
    >
      <div
        class="items-center px-4"
      >
        <svg
          height="55px"
          viewBox="0 0 554.67 497.78"
          width="150px"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g
            style="isolation: isolate;"
          >
            <g
              id="Layer_1"
            >
              <path
                class="fill-current color-primary"
                d="M284.66,226.05h-14.61c-8.86-56.15-59.34-113.23-134.08-113.23S0,170.81,0,248.83v225.81c0,12.78,10.36,23.15,23.15,23.15h44.71c12.78,0,23.15-10.36,23.15-23.15v-97.48c17.09,5.84,34.47,8.4,52.17,7.4,12.83,69.39,71.26,113.22,134.08,113.22,72.7,0,124.96-55.22,134.05-113.02,79.86,3.89,143.37-59.02,143.37-135.8V23.15c0-12.78-10.36-23.15-23.15-23.15h-44.71c-12.78,0-23.15,10.36-23.15,23.15v97.48c-63.3-23.51-160.1,6.59-179.01,105.43ZM463.66,249.02c-.01,24.78-20.18,44.94-44.96,44.94s-44.96-20.17-44.96-44.96,20.17-44.96,44.96-44.96,44.94,20.15,44.96,44.93v.03s0,.01,0,.02ZM23.15,248.85s0,0,0,0v-.06h0s0-.05,0-.08c.04-62.27,50.54-112.74,112.82-112.74,55.62,0,101.84,40.26,111.12,93.22-15.31,3.45-29.84,9.53-43.05,18.01-.85-36.88-30.99-66.52-68.07-66.52s-68.11,30.49-68.11,68.11h0v.04s0,.03,0,.04c0,0,0,0,0,0h0v219.48c0,4.19-3.4,7.59-7.59,7.59h-29.53c-4.19,0-7.59-3.4-7.59-7.59v-219.49ZM91.01,248.76c.02-24.78,20.18-44.93,44.96-44.93s44.96,20.17,44.96,44.96-20.17,44.96-44.96,44.96-44.94-20.15-44.96-44.93v-.03s0-.02,0-.03ZM282.74,249.14c.02,15.89,2.74,31.37,7.96,45.9-26.51-4.9-49.65,5.06-64.2,21.38-34.38,44.6-91.23,55.42-135.49,35.87v-52.36c29.37,25.4,72.86,22.29,98.13-8.6,24.06-28.84,55.05-43.33,93.6-42.18ZM322.22,361.82c0,24.79-20.17,44.96-44.96,44.96s-44.96-20.17-44.96-44.96,20.17-44.96,44.96-44.96,44.96,20.17,44.96,44.96ZM307.6,229.31c16.84-80.82,95.69-109.42,156.07-83.81v52.36c-12-10.56-27.73-16.97-44.96-16.97-37.62,0-68.11,30.49-68.11,68.11,0,16.03,5.55,30.75,14.81,42.38h-.02c21.42,26.73,28.35,59.95,22.96,90.2-9.34,52.89-55.53,93.06-111.09,93.06s-101.84-40.26-111.12-93.22c15.31-3.45,29.84-9.53,43.05-18.01.85,36.88,30.99,66.52,68.07,66.52s68.11-30.49,68.11-68.11c.04-15.85-5.11-29.96-14.9-42.52-20.7-26.78-28.19-56.81-22.87-89.99ZM494.41,21.85h29.52c4.2,0,7.6,3.4,7.6,7.6v219.77c0,69.01-59.45,116.01-118.3,112.45-.02-15.89-2.74-31.37-7.96-45.9,41.14,8.52,81.55-22.74,81.55-66.74V29.44c0-4.2,3.4-7.6,7.6-7.6Z"
                style="mix-blend-mode: multiply;"
              />
            </g>
          </g>
        </svg>
      </div>
      <nav
        class="mt-5 flex-1 px-6 space-y-1"
      >
        <a
          class="text-neutral hover:text-info hover:underline group flex items-center py-2 font-semibold tracking-wide"
          href="/"
        >
          <svg
            aria-hidden="true"
            class="fill-current h-6 text-neutral group-hover:text-info mr-3 shrink-0"
            viewBox="0 0 32 32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M26.17,30.11H7.31A4.09,4.09,0,0,1,3.22,26V13.28A4.1,4.1,0,0,1,4.84,10l9.42-7.15a4.11,4.11,0,0,1,5,0L28.65,10h0a4.12,4.12,0,0,1,1.61,3.26V26A4.1,4.1,0,0,1,26.17,30.11ZM16.74,4a2.13,2.13,0,0,0-1.27.42L6.05,11.61a2.1,2.1,0,0,0-.83,1.67V26a2.1,2.1,0,0,0,2.09,2.09H26.17A2.1,2.1,0,0,0,28.26,26V13.28a2.14,2.14,0,0,0-.82-1.67L18,4.46A2.1,2.1,0,0,0,16.74,4Zm4.47,19.77a4.38,4.38,0,1,1,4.38-4.37A4.37,4.37,0,0,1,21.21,23.81Zm0-6.75a2.38,2.38,0,1,0,2.38,2.38A2.38,2.38,0,0,0,21.21,17.06Z"
              />
            </g>
          </svg>
          Home
        </a>
        <a
          class="text-info font-bold group flex items-center py-2 font-semibold tracking-wide"
          href="/info"
        >
          <svg
            aria-hidden="true"
            class="fill-current h-6 text-info mr-3 shrink-0"
            viewBox="0 0 32 32"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g>
              <path
                d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
              />
              <path
                d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
              />
              <path
                d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
              />
            </g>
          </svg>
          Info
        </a>
      </nav>
    </div>
    <div
      class="px-6 py-4 space-y-1"
    >
      <a
        class="text-neutral hover:text-info hover:underline group flex items-center py-2 font-semibold tracking-wide"
        href="https://www.google.com"
        id="navigation-feedback"
        rel="noreferrer"
        target="_blank"
      >
        <svg
          aria-hidden="true"
          class="fill-current h-6 text-neutral group-hover:text-info mr-3 shrink-0"
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g>
            <path
              d="M27.62,5.55h-5.09v-1.78c0-2.08-1.69-3.78-3.78-3.78H3.78C1.69,0,0,1.69,0,3.78V13.75c0,1.97,1.51,3.59,3.44,3.76l-.03,2.09c0,.36,.19,.7,.51,.87,.14,.08,.3,.12,.46,.12,.19,0,.38-.06,.55-.17l3.93-2.69v1.57c0,2.08,1.69,3.78,3.78,3.78h9.6l4.23,2.89c.17,.11,.36,.17,.55,.17,.16,0,.31-.04,.46-.12,.32-.17,.52-.51,.51-.87l-.03-2.09c1.92-.17,3.44-1.79,3.44-3.76V9.33c0-2.08-1.69-3.78-3.78-3.78ZM8.31,15.75l-2.93,2.01v-1.19c.02-.26-.08-.51-.27-.69-.18-.18-.43-.29-.69-.29h-.65c-1.01,0-1.83-.82-1.83-1.83V3.78c0-1.01,.82-1.83,1.83-1.83h14.98c1.01,0,1.83,.82,1.83,1.83V13.75c0,1.01-.82,1.83-1.83,1.83H8.86c-.2,0-.39,.06-.55,.17Zm21.14,3.55c0,1.01-.82,1.83-1.83,1.83h-.65c-.26,0-.51,.1-.69,.29-.18,.18-.28,.43-.28,.69v1.19s-2.92-2.01-2.92-2.01c-.16-.11-.35-.17-.55-.17H12.64c-1.01,0-1.83-.82-1.83-1.83v-1.78h7.95c2.08,0,3.78-1.69,3.78-3.78V7.5h5.09c1.01,0,1.83,.82,1.83,1.83v9.97Z"
            />
            <path
              d="M5.09,7.2H14.09c.54,0,.97-.44,.97-.97s-.44-.97-.97-.97H5.09c-.54,0-.97,.44-.97,.97s.44,.97,.97,.97Z"
            />
            <path
              d="M9.11,10.23H5.09c-.54,0-.97,.44-.97,.97s.44,.97,.97,.97h4.02c.54,0,.97-.44,.97-.97s-.44-.97-.97-.97Z"
            />
          </g>
        </svg>
        Feedback
      </a>
      <a
        class="text-neutral hover:text-info hover:underline group flex items-center py-2 font-semibold tracking-wide"
        href="/profile"
      >
        <svg
          aria-hidden="true"
          class="fill-current h-6 text-neutral group-hover:text-info mr-3 shrink-0"
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g>
            <path
              d="M15.69,18A8.09,8.09,0,1,1,23.78,9.9,8.1,8.1,0,0,1,15.69,18Zm0-14.17A6.09,6.09,0,1,0,21.78,9.9,6.09,6.09,0,0,0,15.69,3.82Z"
            />
            <path
              d="M2.15,29.62a1.13,1.13,0,0,1-.32-.05A1,1,0,0,1,1.2,28.3c1.92-5.66,8-9.62,14.78-9.62s12.68,3.78,14.71,9.4a1,1,0,0,1-1.89.68c-1.74-4.84-6.89-8.08-12.82-8.08S4.74,24.08,3.1,28.94A1,1,0,0,1,2.15,29.62Z"
            />
          </g>
        </svg>
        Profile
      </a>
      <a
        class="text-neutral hover:text-info hover:underline group flex items-center py-2 font-semibold tracking-wide"
        href="/auth/logout"
      >
        <svg
          aria-hidden="true"
          class="fill-current h-6 text-neutral group-hover:text-info mr-3 shrink-0"
          viewBox="0 0 32 32"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g>
            <path
              d="M24.77,2.4H15a5,5,0,0,0-4.85,5v3.15a1,1,0,0,0,2,0V7.44A3,3,0,0,1,15,4.4h9.74a3,3,0,0,1,2.85,3V24.07a3,3,0,0,1-2.85,3H15a3,3,0,0,1-2.85-3V21.34a1,1,0,1,0-2,0v2.73a5,5,0,0,0,4.85,5h9.74a5,5,0,0,0,4.85-5V7.44A5,5,0,0,0,24.77,2.4Z"
            />
            <path
              d="M5.12,17H16.18a1,1,0,0,0,0-2H5.12L7.5,12.58a1,1,0,0,0,0-1.41,1,1,0,0,0-1.41,0L2,15.26a1,1,0,0,0-.21.32,1,1,0,0,0,0,.77,1,1,0,0,0,.21.32l4.1,4.1A1,1,0,1,0,7.5,19.35Z"
            />
          </g>
        </svg>
        Log out
      </a>
    </div>
    <p
      class="text-xs text-neutral/60 hover:text-info hover:underline ml-1"
    />
  </div>
</body>
`;
