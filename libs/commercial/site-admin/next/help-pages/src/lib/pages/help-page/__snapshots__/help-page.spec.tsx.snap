// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`HelpPage should match snapshot 1`] = `
<body>
  <div>
    <div
      class="space-y-4"
    >
      <header
        class="w-full h-96 bg-navy bg-center bg-contain bg-no-repeat  bg-[url('/hero-background.png')]"
      >
        <div
          class="flex text-center justify-center items-end w-full h-full"
        >
          <h1
            class="text-white text-4xl align-bottom pb-6"
          >
            How can we help you?
          </h1>
        </div>
      </header>
      <section>
        <h2
          class="text-xxl"
        >
          Section links
        </h2>
        <div
          class="pb-4"
        />
        <div
          class="grid grid-cols-6 gap-4 py-2"
        >
          <a
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
            href="#Demo%20videos"
          >
            Demo videos
          </a>
          <a
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
            href="#FAQs"
          >
            FAQs
          </a>
          <a
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
            href="#Pages"
          >
            Pages
          </a>
          <a
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
            href="#Contacting%20us"
          >
            Contacting us
          </a>
        </div>
      </section>
      <section>
        <h2
          class="text-xxl"
        >
          FAQ links
        </h2>
        <div
          class="pb-4"
        />
        <div
          class="grid grid-cols-6 gap-4 py-2"
        >
          <button
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
          >
            The Pod Point network
          </button>
          <button
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
          >
            Revenue and invoicing
          </button>
          <button
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
          >
            Reports and data
          </button>
          <button
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
          >
            Carbon avoided
          </button>
          <button
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
          >
            Drivers
          </button>
          <button
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
          >
            Expenses
          </button>
          <button
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
          >
            Tariffs and pricing
          </button>
          <button
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
          >
            Sites
          </button>
          <button
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
          >
            Chargers
          </button>
          <button
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
          >
            Admins
          </button>
          <button
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
          >
            Insights
          </button>
          <button
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
          >
            Groups
          </button>
          <button
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
          >
            Emails
          </button>
        </div>
      </section>
      <section>
        <h2
          class="text-xxl"
        >
          Pages
        </h2>
        <div
          class="pb-4"
        />
        <div
          class="grid grid-cols-6 gap-4 py-2"
        >
          <button
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
          >
            Home
          </button>
          <button
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
          >
            Sites
          </button>
          <button
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
          >
            Chargers
          </button>
          <button
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
          >
            Drivers
          </button>
          <button
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
          >
            Expenses
          </button>
          <button
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
          >
            Tariffs
          </button>
          <button
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
          >
            Admins
          </button>
          <button
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
          >
            Insights
          </button>
          <button
            class="flex h-24 p-2 rounded-lg bg-white font-bold text-center justify-center items-center hover:underline"
          >
            Groups
          </button>
        </div>
      </section>
      <section
        id="Demo%20videos"
      >
        <h3
          class="text-xxl"
        >
          Demo videos
        </h3>
        <div
          class="pb-4"
        />
        <section
          class="p-3 rounded-lg bg-white"
        >
          <p
            class="text-md font-normal break-words"
          >
            There are no demo videos here yet. We are hard at work to improve this help page and will be adding these soon.
          </p>
        </section>
      </section>
      <section
        id="FAQs"
      >
        <h3
          class="text-xxl"
        >
          FAQs
        </h3>
        <div
          class="pb-4"
        />
        <article
          class="mb-4"
          id="FAQs-The%20Pod%20Point%20network"
        >
          <button
            aria-expanded="false"
            class="flex w-full items-start justify-between text-left text-charcoal"
          >
            <span
              class="font-medium leading-7 ml-4"
            >
              The Pod Point network
            </span>
            <span
              class="ml-6 mr-4 flex h-7 items-center"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m12,18.71c-.38,0-.77-.15-1.06-.44L.53,7.85C-.06,7.27-.06,6.32.53,5.73s1.54-.59,2.12,0l9.35,9.35,9.35-9.35c.59-.59,1.54-.59,2.12,0s.59,1.54,0,2.12l-10.41,10.41c-.29.29-.68.44-1.06.44Z"
                />
              </svg>
            </span>
          </button>
          <hr
            class="h-px mt-2 bg-neutral/40 border-0"
          />
        </article>
        <article
          class="mb-4"
          id="FAQs-Revenue%20and%20invoicing"
        >
          <button
            aria-expanded="false"
            class="flex w-full items-start justify-between text-left text-charcoal"
          >
            <span
              class="font-medium leading-7 ml-4"
            >
              Revenue and invoicing
            </span>
            <span
              class="ml-6 mr-4 flex h-7 items-center"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m12,18.71c-.38,0-.77-.15-1.06-.44L.53,7.85C-.06,7.27-.06,6.32.53,5.73s1.54-.59,2.12,0l9.35,9.35,9.35-9.35c.59-.59,1.54-.59,2.12,0s.59,1.54,0,2.12l-10.41,10.41c-.29.29-.68.44-1.06.44Z"
                />
              </svg>
            </span>
          </button>
          <hr
            class="h-px mt-2 bg-neutral/40 border-0"
          />
        </article>
        <article
          class="mb-4"
          id="FAQs-Reports%20and%20data"
        >
          <button
            aria-expanded="false"
            class="flex w-full items-start justify-between text-left text-charcoal"
          >
            <span
              class="font-medium leading-7 ml-4"
            >
              Reports and data
            </span>
            <span
              class="ml-6 mr-4 flex h-7 items-center"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m12,18.71c-.38,0-.77-.15-1.06-.44L.53,7.85C-.06,7.27-.06,6.32.53,5.73s1.54-.59,2.12,0l9.35,9.35,9.35-9.35c.59-.59,1.54-.59,2.12,0s.59,1.54,0,2.12l-10.41,10.41c-.29.29-.68.44-1.06.44Z"
                />
              </svg>
            </span>
          </button>
          <hr
            class="h-px mt-2 bg-neutral/40 border-0"
          />
        </article>
        <article
          class="mb-4"
          id="FAQs-Carbon%20avoided"
        >
          <button
            aria-expanded="false"
            class="flex w-full items-start justify-between text-left text-charcoal"
          >
            <span
              class="font-medium leading-7 ml-4"
            >
              Carbon avoided
            </span>
            <span
              class="ml-6 mr-4 flex h-7 items-center"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m12,18.71c-.38,0-.77-.15-1.06-.44L.53,7.85C-.06,7.27-.06,6.32.53,5.73s1.54-.59,2.12,0l9.35,9.35,9.35-9.35c.59-.59,1.54-.59,2.12,0s.59,1.54,0,2.12l-10.41,10.41c-.29.29-.68.44-1.06.44Z"
                />
              </svg>
            </span>
          </button>
          <hr
            class="h-px mt-2 bg-neutral/40 border-0"
          />
        </article>
        <article
          class="mb-4"
          id="FAQs-Drivers"
        >
          <button
            aria-expanded="false"
            class="flex w-full items-start justify-between text-left text-charcoal"
          >
            <span
              class="font-medium leading-7 ml-4"
            >
              Drivers
            </span>
            <span
              class="ml-6 mr-4 flex h-7 items-center"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m12,18.71c-.38,0-.77-.15-1.06-.44L.53,7.85C-.06,7.27-.06,6.32.53,5.73s1.54-.59,2.12,0l9.35,9.35,9.35-9.35c.59-.59,1.54-.59,2.12,0s.59,1.54,0,2.12l-10.41,10.41c-.29.29-.68.44-1.06.44Z"
                />
              </svg>
            </span>
          </button>
          <hr
            class="h-px mt-2 bg-neutral/40 border-0"
          />
        </article>
        <article
          class="mb-4"
          id="FAQs-Expenses"
        >
          <button
            aria-expanded="false"
            class="flex w-full items-start justify-between text-left text-charcoal"
          >
            <span
              class="font-medium leading-7 ml-4"
            >
              Expenses
            </span>
            <span
              class="ml-6 mr-4 flex h-7 items-center"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m12,18.71c-.38,0-.77-.15-1.06-.44L.53,7.85C-.06,7.27-.06,6.32.53,5.73s1.54-.59,2.12,0l9.35,9.35,9.35-9.35c.59-.59,1.54-.59,2.12,0s.59,1.54,0,2.12l-10.41,10.41c-.29.29-.68.44-1.06.44Z"
                />
              </svg>
            </span>
          </button>
          <hr
            class="h-px mt-2 bg-neutral/40 border-0"
          />
        </article>
        <article
          class="mb-4"
          id="FAQs-Tariffs%20and%20pricing"
        >
          <button
            aria-expanded="false"
            class="flex w-full items-start justify-between text-left text-charcoal"
          >
            <span
              class="font-medium leading-7 ml-4"
            >
              Tariffs and pricing
            </span>
            <span
              class="ml-6 mr-4 flex h-7 items-center"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m12,18.71c-.38,0-.77-.15-1.06-.44L.53,7.85C-.06,7.27-.06,6.32.53,5.73s1.54-.59,2.12,0l9.35,9.35,9.35-9.35c.59-.59,1.54-.59,2.12,0s.59,1.54,0,2.12l-10.41,10.41c-.29.29-.68.44-1.06.44Z"
                />
              </svg>
            </span>
          </button>
          <hr
            class="h-px mt-2 bg-neutral/40 border-0"
          />
        </article>
        <article
          class="mb-4"
          id="FAQs-Sites"
        >
          <button
            aria-expanded="false"
            class="flex w-full items-start justify-between text-left text-charcoal"
          >
            <span
              class="font-medium leading-7 ml-4"
            >
              Sites
            </span>
            <span
              class="ml-6 mr-4 flex h-7 items-center"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m12,18.71c-.38,0-.77-.15-1.06-.44L.53,7.85C-.06,7.27-.06,6.32.53,5.73s1.54-.59,2.12,0l9.35,9.35,9.35-9.35c.59-.59,1.54-.59,2.12,0s.59,1.54,0,2.12l-10.41,10.41c-.29.29-.68.44-1.06.44Z"
                />
              </svg>
            </span>
          </button>
          <hr
            class="h-px mt-2 bg-neutral/40 border-0"
          />
        </article>
        <article
          class="mb-4"
          id="FAQs-Chargers"
        >
          <button
            aria-expanded="false"
            class="flex w-full items-start justify-between text-left text-charcoal"
          >
            <span
              class="font-medium leading-7 ml-4"
            >
              Chargers
            </span>
            <span
              class="ml-6 mr-4 flex h-7 items-center"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m12,18.71c-.38,0-.77-.15-1.06-.44L.53,7.85C-.06,7.27-.06,6.32.53,5.73s1.54-.59,2.12,0l9.35,9.35,9.35-9.35c.59-.59,1.54-.59,2.12,0s.59,1.54,0,2.12l-10.41,10.41c-.29.29-.68.44-1.06.44Z"
                />
              </svg>
            </span>
          </button>
          <hr
            class="h-px mt-2 bg-neutral/40 border-0"
          />
        </article>
        <article
          class="mb-4"
          id="FAQs-Admins"
        >
          <button
            aria-expanded="false"
            class="flex w-full items-start justify-between text-left text-charcoal"
          >
            <span
              class="font-medium leading-7 ml-4"
            >
              Admins
            </span>
            <span
              class="ml-6 mr-4 flex h-7 items-center"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m12,18.71c-.38,0-.77-.15-1.06-.44L.53,7.85C-.06,7.27-.06,6.32.53,5.73s1.54-.59,2.12,0l9.35,9.35,9.35-9.35c.59-.59,1.54-.59,2.12,0s.59,1.54,0,2.12l-10.41,10.41c-.29.29-.68.44-1.06.44Z"
                />
              </svg>
            </span>
          </button>
          <hr
            class="h-px mt-2 bg-neutral/40 border-0"
          />
        </article>
        <article
          class="mb-4"
          id="FAQs-Insights"
        >
          <button
            aria-expanded="false"
            class="flex w-full items-start justify-between text-left text-charcoal"
          >
            <span
              class="font-medium leading-7 ml-4"
            >
              Insights
            </span>
            <span
              class="ml-6 mr-4 flex h-7 items-center"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m12,18.71c-.38,0-.77-.15-1.06-.44L.53,7.85C-.06,7.27-.06,6.32.53,5.73s1.54-.59,2.12,0l9.35,9.35,9.35-9.35c.59-.59,1.54-.59,2.12,0s.59,1.54,0,2.12l-10.41,10.41c-.29.29-.68.44-1.06.44Z"
                />
              </svg>
            </span>
          </button>
          <hr
            class="h-px mt-2 bg-neutral/40 border-0"
          />
        </article>
        <article
          class="mb-4"
          id="FAQs-Groups"
        >
          <button
            aria-expanded="false"
            class="flex w-full items-start justify-between text-left text-charcoal"
          >
            <span
              class="font-medium leading-7 ml-4"
            >
              Groups
            </span>
            <span
              class="ml-6 mr-4 flex h-7 items-center"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m12,18.71c-.38,0-.77-.15-1.06-.44L.53,7.85C-.06,7.27-.06,6.32.53,5.73s1.54-.59,2.12,0l9.35,9.35,9.35-9.35c.59-.59,1.54-.59,2.12,0s.59,1.54,0,2.12l-10.41,10.41c-.29.29-.68.44-1.06.44Z"
                />
              </svg>
            </span>
          </button>
          <hr
            class="h-px mt-2 bg-neutral/40 border-0"
          />
        </article>
        <article
          class="mb-4"
          id="FAQs-Emails"
        >
          <button
            aria-expanded="false"
            class="flex w-full items-start justify-between text-left text-charcoal"
          >
            <span
              class="font-medium leading-7 ml-4"
            >
              Emails
            </span>
            <span
              class="ml-6 mr-4 flex h-7 items-center"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m12,18.71c-.38,0-.77-.15-1.06-.44L.53,7.85C-.06,7.27-.06,6.32.53,5.73s1.54-.59,2.12,0l9.35,9.35,9.35-9.35c.59-.59,1.54-.59,2.12,0s.59,1.54,0,2.12l-10.41,10.41c-.29.29-.68.44-1.06.44Z"
                />
              </svg>
            </span>
          </button>
          <hr
            class="h-px mt-2 bg-neutral/40 border-0"
          />
        </article>
      </section>
      <section
        id="Pages"
      >
        <h3
          class="text-xxl"
        >
          Pages
        </h3>
        <div
          class="pb-4"
        />
        <article
          class="mb-4"
          id="Pages-Home"
        >
          <button
            aria-expanded="false"
            class="flex w-full items-start justify-between text-left text-charcoal"
          >
            <span
              class="font-medium leading-7 ml-4"
            >
              Home
            </span>
            <span
              class="ml-6 mr-4 flex h-7 items-center"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m12,18.71c-.38,0-.77-.15-1.06-.44L.53,7.85C-.06,7.27-.06,6.32.53,5.73s1.54-.59,2.12,0l9.35,9.35,9.35-9.35c.59-.59,1.54-.59,2.12,0s.59,1.54,0,2.12l-10.41,10.41c-.29.29-.68.44-1.06.44Z"
                />
              </svg>
            </span>
          </button>
          <hr
            class="h-px mt-2 bg-neutral/40 border-0"
          />
        </article>
        <article
          class="mb-4"
          id="Pages-Sites"
        >
          <button
            aria-expanded="false"
            class="flex w-full items-start justify-between text-left text-charcoal"
          >
            <span
              class="font-medium leading-7 ml-4"
            >
              Sites
            </span>
            <span
              class="ml-6 mr-4 flex h-7 items-center"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m12,18.71c-.38,0-.77-.15-1.06-.44L.53,7.85C-.06,7.27-.06,6.32.53,5.73s1.54-.59,2.12,0l9.35,9.35,9.35-9.35c.59-.59,1.54-.59,2.12,0s.59,1.54,0,2.12l-10.41,10.41c-.29.29-.68.44-1.06.44Z"
                />
              </svg>
            </span>
          </button>
          <hr
            class="h-px mt-2 bg-neutral/40 border-0"
          />
        </article>
        <article
          class="mb-4"
          id="Pages-Chargers"
        >
          <button
            aria-expanded="false"
            class="flex w-full items-start justify-between text-left text-charcoal"
          >
            <span
              class="font-medium leading-7 ml-4"
            >
              Chargers
            </span>
            <span
              class="ml-6 mr-4 flex h-7 items-center"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m12,18.71c-.38,0-.77-.15-1.06-.44L.53,7.85C-.06,7.27-.06,6.32.53,5.73s1.54-.59,2.12,0l9.35,9.35,9.35-9.35c.59-.59,1.54-.59,2.12,0s.59,1.54,0,2.12l-10.41,10.41c-.29.29-.68.44-1.06.44Z"
                />
              </svg>
            </span>
          </button>
          <hr
            class="h-px mt-2 bg-neutral/40 border-0"
          />
        </article>
        <article
          class="mb-4"
          id="Pages-Drivers"
        >
          <button
            aria-expanded="false"
            class="flex w-full items-start justify-between text-left text-charcoal"
          >
            <span
              class="font-medium leading-7 ml-4"
            >
              Drivers
            </span>
            <span
              class="ml-6 mr-4 flex h-7 items-center"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m12,18.71c-.38,0-.77-.15-1.06-.44L.53,7.85C-.06,7.27-.06,6.32.53,5.73s1.54-.59,2.12,0l9.35,9.35,9.35-9.35c.59-.59,1.54-.59,2.12,0s.59,1.54,0,2.12l-10.41,10.41c-.29.29-.68.44-1.06.44Z"
                />
              </svg>
            </span>
          </button>
          <hr
            class="h-px mt-2 bg-neutral/40 border-0"
          />
        </article>
        <article
          class="mb-4"
          id="Pages-Expenses"
        >
          <button
            aria-expanded="false"
            class="flex w-full items-start justify-between text-left text-charcoal"
          >
            <span
              class="font-medium leading-7 ml-4"
            >
              Expenses
            </span>
            <span
              class="ml-6 mr-4 flex h-7 items-center"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m12,18.71c-.38,0-.77-.15-1.06-.44L.53,7.85C-.06,7.27-.06,6.32.53,5.73s1.54-.59,2.12,0l9.35,9.35,9.35-9.35c.59-.59,1.54-.59,2.12,0s.59,1.54,0,2.12l-10.41,10.41c-.29.29-.68.44-1.06.44Z"
                />
              </svg>
            </span>
          </button>
          <hr
            class="h-px mt-2 bg-neutral/40 border-0"
          />
        </article>
        <article
          class="mb-4"
          id="Pages-Tariffs"
        >
          <button
            aria-expanded="false"
            class="flex w-full items-start justify-between text-left text-charcoal"
          >
            <span
              class="font-medium leading-7 ml-4"
            >
              Tariffs
            </span>
            <span
              class="ml-6 mr-4 flex h-7 items-center"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m12,18.71c-.38,0-.77-.15-1.06-.44L.53,7.85C-.06,7.27-.06,6.32.53,5.73s1.54-.59,2.12,0l9.35,9.35,9.35-9.35c.59-.59,1.54-.59,2.12,0s.59,1.54,0,2.12l-10.41,10.41c-.29.29-.68.44-1.06.44Z"
                />
              </svg>
            </span>
          </button>
          <hr
            class="h-px mt-2 bg-neutral/40 border-0"
          />
        </article>
        <article
          class="mb-4"
          id="Pages-Admins"
        >
          <button
            aria-expanded="false"
            class="flex w-full items-start justify-between text-left text-charcoal"
          >
            <span
              class="font-medium leading-7 ml-4"
            >
              Admins
            </span>
            <span
              class="ml-6 mr-4 flex h-7 items-center"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m12,18.71c-.38,0-.77-.15-1.06-.44L.53,7.85C-.06,7.27-.06,6.32.53,5.73s1.54-.59,2.12,0l9.35,9.35,9.35-9.35c.59-.59,1.54-.59,2.12,0s.59,1.54,0,2.12l-10.41,10.41c-.29.29-.68.44-1.06.44Z"
                />
              </svg>
            </span>
          </button>
          <hr
            class="h-px mt-2 bg-neutral/40 border-0"
          />
        </article>
        <article
          class="mb-4"
          id="Pages-Insights"
        >
          <button
            aria-expanded="false"
            class="flex w-full items-start justify-between text-left text-charcoal"
          >
            <span
              class="font-medium leading-7 ml-4"
            >
              Insights
            </span>
            <span
              class="ml-6 mr-4 flex h-7 items-center"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m12,18.71c-.38,0-.77-.15-1.06-.44L.53,7.85C-.06,7.27-.06,6.32.53,5.73s1.54-.59,2.12,0l9.35,9.35,9.35-9.35c.59-.59,1.54-.59,2.12,0s.59,1.54,0,2.12l-10.41,10.41c-.29.29-.68.44-1.06.44Z"
                />
              </svg>
            </span>
          </button>
          <hr
            class="h-px mt-2 bg-neutral/40 border-0"
          />
        </article>
        <article
          class="mb-4"
          id="Pages-Groups"
        >
          <button
            aria-expanded="false"
            class="flex w-full items-start justify-between text-left text-charcoal"
          >
            <span
              class="font-medium leading-7 ml-4"
            >
              Groups
            </span>
            <span
              class="ml-6 mr-4 flex h-7 items-center"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="m12,18.71c-.38,0-.77-.15-1.06-.44L.53,7.85C-.06,7.27-.06,6.32.53,5.73s1.54-.59,2.12,0l9.35,9.35,9.35-9.35c.59-.59,1.54-.59,2.12,0s.59,1.54,0,2.12l-10.41,10.41c-.29.29-.68.44-1.06.44Z"
                />
              </svg>
            </span>
          </button>
          <hr
            class="h-px mt-2 bg-neutral/40 border-0"
          />
        </article>
      </section>
      <section
        id="Contacting%20us"
      >
        <h3
          class="text-xxl"
        >
          Contacting us
        </h3>
        <div
          class="pb-4"
        />
        <section
          class="p-3 rounded-lg bg-white"
        >
          <p
            class="text-md font-normal break-words"
          >
            You can get in touch with us by phoning our Support team on 020 7247 4114 or using the "Report an issue" button in the service. EV drivers can report problems with chargers themselves via the app, too.
          </p>
          <p
            class="text-md font-normal break-words"
          >
            Take a look below to be sure you're reaching out to the right team for your query.
          </p>
          <ul
            class="list-disc list-inside"
          >
            <li>
              Faulty charger queries & reports, Maintenance update requests - Customer Support & Technical Team via the "Report an issue" button
            </li>
            <li>
              Warranty, Servicing & Maintenance - Aftersales Account Manager
            </li>
            <li>
              Site Management Service related support - Customer Success Management via the "Report an issue" button
            </li>
            <li>
              Invoice & Statement Queries - Accounts <NAME_EMAIL>
            </li>
            <li>
              New or existing sales & passive bay activation - Commercial Sales team
            </li>
          </ul>
        </section>
      </section>
      <div
        class="pb-4"
      />
    </div>
  </div>
</body>
`;
