import { ErrorBoundary } from '@sentry/react';
import {
  TARIFF_INVALID_TARIFF_TIER_ERROR,
  TARIFF_PER_KWH_PRICE_ERROR,
  TARIFF_PRICE_MUST_NOT_BE_LESS_THAN_1P_IF_NOT_FREE_ERROR,
  TARIFF_SCHEDULE_LIMIT_ERROR,
  TARIFF_SCHEDULE_OVERLAP_ERROR,
  TariffScheduleErrorCodes,
} from '@experience/commercial/site-admin/typescript/domain-model-validation';
import {
  TEST_POD_WITH_OCPP_SUPPORT,
  TEST_TARIFF,
  TEST_TARIFF_WITH_EMPTY_SCHEDULES,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { mutate } from 'swr';
import AddWorkWeekTariffScheduleModal from './add-work-week-tariff-schedule-modal';
import axios from 'axios';
import userEvent from '@testing-library/user-event';

jest.mock('axios');
const mockAxiosPost = jest.mocked(axios.post);

jest.mock('swr');
const mockMutate = jest.mocked(mutate);

const mockSetOpen = jest.fn();

const defaultProps = {
  tariff: TEST_TARIFF_WITH_EMPTY_SCHEDULES,
  open: true,
  setOpen: mockSetOpen,
};

describe('AddWorkWeekTariffScheduleModal', () => {
  it('should render correctly', () => {
    const { baseElement } = render(
      <AddWorkWeekTariffScheduleModal {...defaultProps} />
    );

    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(
      <AddWorkWeekTariffScheduleModal {...defaultProps} />
    );

    expect(baseElement).toMatchSnapshot();
  });

  it('should close modal and clear inputs if cancel button is clicked', () => {
    render(<AddWorkWeekTariffScheduleModal {...defaultProps} />);

    fireEvent.click(screen.getByLabelText('Tariff tier'));
    fireEvent.click(screen.getByRole('option', { name: 'Member' }));

    fireEvent.change(
      screen.getByLabelText('Weekday price per kWh (in pence)'),
      {
        target: { value: '50.123' },
      }
    );

    fireEvent.change(
      screen.getByLabelText('Weekend price per kWh (in pence)'),
      {
        target: { value: '20.321' },
      }
    );

    fireEvent.click(screen.getByRole('button', { name: 'Cancel' }));

    expect(mockSetOpen).toHaveBeenCalledWith(false);
    expect(
      screen.getByRole('button', { name: 'Tariff tier' })
    ).toBeInTheDocument();

    expect(
      screen.getByLabelText('Weekday price per kWh (in pence)')
    ).toHaveValue(undefined);
    expect(
      screen.getByLabelText('Weekend price per kWh (in pence)')
    ).toHaveValue(undefined);
  });

  it.each([
    ['positive prices', '1', '1'],
    ['prices of zero', '0', '0'],
  ])(
    'should call the API when the form is submitted with %s',
    async (_, weekdayPrice, weekendPrice) => {
      render(<AddWorkWeekTariffScheduleModal {...defaultProps} />);

      const submitButton = screen.getByRole('button', { name: 'Add schedule' });

      fireEvent.click(screen.getByLabelText('Tariff tier'));
      fireEvent.click(screen.getByRole('option', { name: 'Member' }));

      fireEvent.change(
        screen.getByLabelText('Weekday price per kWh (in pence)'),
        {
          target: { value: weekdayPrice },
        }
      );

      fireEvent.change(
        screen.getByLabelText('Weekend price per kWh (in pence)'),
        {
          target: { value: weekendPrice },
        }
      );

      fireEvent.click(submitButton);

      expect(submitButton).toBeDisabled();

      await waitFor(() => {
        expect(mockAxiosPost).toHaveBeenCalledWith(
          `/api/tariffs/${TEST_TARIFF.id}/schedules/workweek`,
          {
            tariffTier: 'members',
            weekdayPrice,
            weekendPrice,
          }
        );
        expect(mockMutate).toHaveBeenCalledWith(
          `/api/tariffs/${TEST_TARIFF.id}`
        );
        expect(mockSetOpen).toHaveBeenCalledWith(false);
        expect(submitButton).not.toBeDisabled();
      });
    }
  );

  it.each(['Weekday', 'Weekend'])(
    'should throw a validation error if the %s price is less than 1p (if not free)',
    async (day) => {
      render(<AddWorkWeekTariffScheduleModal {...defaultProps} />);

      fireEvent.change(
        screen.getByLabelText(`${day} price per kWh (in pence)`),
        {
          target: { value: '0.999' },
        }
      );

      fireEvent.click(screen.getByRole('button', { name: 'Add schedule' }));

      expect(
        await screen.findByText(
          TARIFF_PRICE_MUST_NOT_BE_LESS_THAN_1P_IF_NOT_FREE_ERROR
        )
      ).toBeInTheDocument();
    }
  );

  it.each(['Weekday', 'Weekend'])(
    'should throw a validation error if the %s price is invalid',
    async (day) => {
      render(<AddWorkWeekTariffScheduleModal {...defaultProps} />);

      fireEvent.change(
        screen.getByLabelText(`${day} price per kWh (in pence)`),
        {
          target: { value: '10.12345' },
        }
      );

      fireEvent.click(screen.getByRole('button', { name: 'Add schedule' }));

      expect(
        await screen.findByText(TARIFF_PER_KWH_PRICE_ERROR)
      ).toBeInTheDocument();
    }
  );

  it('should throw a validation error if the %s price is above the limit', async () => {
    render(<AddWorkWeekTariffScheduleModal {...defaultProps} />);

    fireEvent.click(screen.getByLabelText('Tariff tier'));
    fireEvent.click(screen.getByRole('option', { name: 'Member' }));

    fireEvent.change(
      screen.getByLabelText('Weekday price per kWh (in pence)'),
      {
        target: { value: '201' },
      }
    );

    fireEvent.change(
      screen.getByLabelText('Weekend price per kWh (in pence)'),
      {
        target: { value: '201' },
      }
    );

    fireEvent.click(screen.getByRole('button', { name: 'Add schedule' }));
    expect(
      await screen.findAllByText(TARIFF_SCHEDULE_LIMIT_ERROR('£2.00'))
    ).toHaveLength(2);
  });

  it('should handle errors and direct to error boundary', async () => {
    mockAxiosPost.mockRejectedValueOnce({
      response: {
        status: 500,
      },
    });

    render(
      <ErrorBoundary fallback={<h1>Error detected</h1>}>
        <AddWorkWeekTariffScheduleModal {...defaultProps} />
      </ErrorBoundary>
    );

    fireEvent.click(screen.getByLabelText('Tariff tier'));
    fireEvent.click(screen.getByRole('option', { name: 'Member' }));

    fireEvent.change(
      screen.getByLabelText('Weekday price per kWh (in pence)'),
      {
        target: { value: '50' },
      }
    );

    fireEvent.change(
      screen.getByLabelText('Weekend price per kWh (in pence)'),
      {
        target: { value: '20' },
      }
    );

    fireEvent.click(screen.getByRole('button', { name: 'Add schedule' }));

    expect(
      await screen.findByRole('heading', { level: 1, name: 'Error detected' })
    ).toBeInTheDocument();
  });

  it('should throw a validation error for each of the fields', async () => {
    render(<AddWorkWeekTariffScheduleModal {...defaultProps} />);

    fireEvent.click(screen.getByRole('button', { name: 'Add schedule' }));

    expect(
      await screen.findByText(TARIFF_INVALID_TARIFF_TIER_ERROR)
    ).toBeInTheDocument();
    expect(await screen.findAllByText('This field is required')).toHaveLength(
      2
    );
  });

  it('should show validation message on form submission when the tariff overlaps with an existing one', async () => {
    mockAxiosPost.mockRejectedValueOnce({
      response: {
        data: {
          error: TariffScheduleErrorCodes.SCHEDULE_OVERLAP_ERROR,
          message: TARIFF_SCHEDULE_OVERLAP_ERROR,
          statusCode: 403,
        },
        status: 403,
      },
    });

    render(<AddWorkWeekTariffScheduleModal {...defaultProps} />);

    fireEvent.click(screen.getByLabelText('Tariff tier'));
    fireEvent.click(screen.getByRole('option', { name: 'Member' }));

    fireEvent.change(
      screen.getByLabelText('Weekday price per kWh (in pence)'),
      {
        target: { value: '50.123' },
      }
    );

    fireEvent.change(
      screen.getByLabelText('Weekend price per kWh (in pence)'),
      {
        target: { value: '20.321' },
      }
    );

    fireEvent.click(screen.getByRole('button', { name: 'Add schedule' }));

    expect(
      await screen.findByText(TARIFF_SCHEDULE_OVERLAP_ERROR)
    ).toBeInTheDocument();
    expect(screen.getByLabelText('Tariff tier')).toHaveClass(
      'border-2 border-solid border-error'
    );
  });

  it('should autofocus the first input', () => {
    render(<AddWorkWeekTariffScheduleModal {...defaultProps} />);
    expect(screen.getByLabelText('Tariff tier')).toHaveFocus();
  });

  it('should disable confirm button if charger supports ocpp', () => {
    render(
      <AddWorkWeekTariffScheduleModal
        {...defaultProps}
        tariff={{ ...TEST_TARIFF, pods: [TEST_POD_WITH_OCPP_SUPPORT] }}
      />
    );

    expect(screen.getByRole('button', { name: 'Add schedule' })).toBeDisabled();
  });

  it.each(['Public', 'Driver', 'Member'])(
    'should disable confirm button if there is already a schedule of that tier already present in the tariff',
    (tier) => {
      render(
        <AddWorkWeekTariffScheduleModal
          {...defaultProps}
          tariff={TEST_TARIFF}
        />
      );

      fireEvent.click(screen.getByLabelText('Tariff tier'));
      fireEvent.click(screen.getByRole('option', { name: tier }));

      expect(
        screen.getByRole('button', { name: 'Add schedule' })
      ).toBeDisabled();
    }
  );

  it.each(['Public', 'Driver', 'Member'])(
    'should be disabled and show tooltip on hover if a schedule is present for that tariff tier',
    async (tier) => {
      render(
        <AddWorkWeekTariffScheduleModal
          {...defaultProps}
          tariff={TEST_TARIFF}
        />
      );

      fireEvent.click(screen.getByLabelText('Tariff tier'));
      fireEvent.click(screen.getByRole('option', { name: tier }));

      await userEvent.hover(screen.getByRole('tooltip'));
      expect(
        await screen.findByText(
          'Any existing schedules for this tariff tier must be deleted before this schedule can be added'
        )
      ).toBeInTheDocument();
    }
  );
});
