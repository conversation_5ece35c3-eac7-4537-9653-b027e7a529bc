import { COMMON_REQUIRED_ERROR } from '@experience/shared/typescript/validation';
import { ErrorBoundary } from '@sentry/react';
import {
  TARIFF_INVALID_TARIFF_TIER_ERROR,
  TARIFF_PER_KWH_PRICE_ERROR,
  TARIFF_PRICE_MUST_NOT_BE_LESS_THAN_1P_IF_NOT_FREE_ERROR,
  TARIFF_SCHEDULE_LIMIT_ERROR,
  TARIFF_SCHEDULE_OVERLAP_ERROR,
  TariffScheduleErrorCodes,
} from '@experience/commercial/site-admin/typescript/domain-model-validation';
import {
  TEST_POD_WITH_OCPP_SUPPORT,
  TEST_TARIFF,
  TEST_TARIFF_WITH_EMPTY_SCHEDULES,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { mutate } from 'swr';
import AddFlatRateTariffScheduleModal from './add-flat-rate-tariff-schedule-modal';
import axios from 'axios';
import userEvent from '@testing-library/user-event';

jest.mock('axios');
const mockAxiosPost = jest.mocked(axios.post);

jest.mock('swr');
const mockMutate = jest.mocked(mutate);

const mockSetOpen = jest.fn();

const defaultProps = {
  tariff: TEST_TARIFF_WITH_EMPTY_SCHEDULES,
  open: true,
  setOpen: mockSetOpen,
};

describe('AddFlatRateTariffScheduleModal', () => {
  it('should render correctly', () => {
    const { baseElement } = render(
      <AddFlatRateTariffScheduleModal {...defaultProps} />
    );

    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(
      <AddFlatRateTariffScheduleModal {...defaultProps} />
    );

    expect(baseElement).toMatchSnapshot();
  });

  it('should close modal and clear inputs if cancel button is clicked', () => {
    render(<AddFlatRateTariffScheduleModal {...defaultProps} />);

    fireEvent.click(screen.getByLabelText('Tariff tier'));
    fireEvent.click(screen.getByRole('option', { name: 'Member' }));

    fireEvent.change(screen.getByLabelText('Price per kWh (in pence)'), {
      target: { value: '20' },
    });

    fireEvent.click(screen.getByRole('button', { name: 'Cancel' }));

    expect(mockSetOpen).toHaveBeenCalledWith(false);
    expect(
      screen.getByRole('button', { name: 'Tariff tier' })
    ).toBeInTheDocument();
    expect(screen.getByLabelText('Price per kWh (in pence)')).toHaveValue(
      undefined
    );
  });

  it.each([
    ['a positive value', '1'],
    ['a value of zero', '0'],
  ])(
    'should call the API when the form is submitted when price is %s',
    async (_, price) => {
      render(<AddFlatRateTariffScheduleModal {...defaultProps} />);

      const submitButton = screen.getByRole('button', { name: 'Add schedule' });

      fireEvent.click(screen.getByLabelText('Tariff tier'));
      fireEvent.click(screen.getByRole('option', { name: 'Member' }));

      fireEvent.change(screen.getByLabelText('Price per kWh (in pence)'), {
        target: { value: price },
      });

      fireEvent.click(submitButton);

      expect(submitButton).toBeDisabled();

      await waitFor(() => {
        expect(mockAxiosPost).toHaveBeenCalledWith(
          `/api/tariffs/${TEST_TARIFF.id}/schedules`,
          {
            endDay: 0,
            endTime: '00:00',
            price,
            pricingModel: 'energy',
            startDay: 0,
            startTime: '00:00',
            tariffTier: 'members',
          }
        );
        expect(mockMutate).toHaveBeenCalledWith(
          `/api/tariffs/${TEST_TARIFF.id}`
        );
        expect(mockSetOpen).toHaveBeenCalledWith(false);
        expect(submitButton).not.toBeDisabled();
      });
    }
  );

  it('should throw a validation error if the price is less than 1p (if not free)', async () => {
    render(<AddFlatRateTariffScheduleModal {...defaultProps} />);

    fireEvent.change(screen.getByLabelText('Price per kWh (in pence)'), {
      target: { value: '0.999' },
    });

    fireEvent.click(screen.getByRole('button', { name: 'Add schedule' }));

    expect(
      await screen.findByText(
        TARIFF_PRICE_MUST_NOT_BE_LESS_THAN_1P_IF_NOT_FREE_ERROR
      )
    ).toBeInTheDocument();
  });

  it('should throw a validation error if the price has more than three decimal places', async () => {
    render(<AddFlatRateTariffScheduleModal {...defaultProps} />);

    fireEvent.change(screen.getByLabelText('Price per kWh (in pence)'), {
      target: { value: '10.1234' },
    });

    fireEvent.click(screen.getByRole('button', { name: 'Add schedule' }));

    expect(
      await screen.findByText(TARIFF_PER_KWH_PRICE_ERROR)
    ).toBeInTheDocument();
  });

  it('should throw a validation error if the pricing model is energy and price is greater than limit', async () => {
    render(<AddFlatRateTariffScheduleModal {...defaultProps} />);

    fireEvent.change(screen.getByLabelText('Price per kWh (in pence)'), {
      target: { value: '2001' },
    });

    fireEvent.click(screen.getByRole('button', { name: 'Add schedule' }));
    expect(
      await screen.findByText(TARIFF_SCHEDULE_LIMIT_ERROR('£2.00'))
    ).toBeInTheDocument();
  });

  it('should handle errors and direct to error boundary', async () => {
    mockAxiosPost.mockRejectedValueOnce({
      response: {
        status: 500,
      },
    });

    render(
      <ErrorBoundary fallback={<h1>Error detected</h1>}>
        <AddFlatRateTariffScheduleModal {...defaultProps} />
      </ErrorBoundary>
    );

    fireEvent.click(screen.getByLabelText('Tariff tier'));
    fireEvent.click(screen.getByRole('option', { name: 'Member' }));

    fireEvent.change(screen.getByLabelText('Price per kWh (in pence)'), {
      target: { value: '20' },
    });

    fireEvent.click(screen.getByRole('button', { name: 'Add schedule' }));

    expect(
      await screen.findByRole('heading', { level: 1, name: 'Error detected' })
    ).toBeInTheDocument();
  });

  it('should show validation message on form submission when the tariff overlaps with an existing one', async () => {
    mockAxiosPost.mockRejectedValueOnce({
      response: {
        data: {
          error: TariffScheduleErrorCodes.SCHEDULE_OVERLAP_ERROR,
          message: TARIFF_SCHEDULE_OVERLAP_ERROR,
          statusCode: 403,
        },
        status: 403,
      },
    });

    render(<AddFlatRateTariffScheduleModal {...defaultProps} />);

    fireEvent.click(screen.getByLabelText('Tariff tier'));
    fireEvent.click(screen.getByRole('option', { name: 'Member' }));

    fireEvent.change(screen.getByLabelText('Price per kWh (in pence)'), {
      target: { value: '20' },
    });

    fireEvent.click(screen.getByRole('button', { name: 'Add schedule' }));

    expect(
      await screen.findByText(TARIFF_SCHEDULE_OVERLAP_ERROR)
    ).toBeInTheDocument();
    expect(screen.getByLabelText('Price per kWh (in pence)')).toHaveClass(
      'border-2 border-solid border-error'
    );
  });

  it('should throw a validation error for each of the fields', async () => {
    render(<AddFlatRateTariffScheduleModal {...defaultProps} />);

    fireEvent.click(screen.getByRole('button', { name: 'Add schedule' }));

    expect(
      await screen.findByText(TARIFF_INVALID_TARIFF_TIER_ERROR)
    ).toBeInTheDocument();
    expect(await screen.findAllByText(COMMON_REQUIRED_ERROR)).toHaveLength(1);
  });

  it('should autofocus the first input', () => {
    render(<AddFlatRateTariffScheduleModal {...defaultProps} />);
    expect(screen.getByLabelText('Tariff tier')).toHaveFocus();
  });

  it('should disable confirm button if charger supports ocpp', () => {
    render(
      <AddFlatRateTariffScheduleModal
        {...defaultProps}
        tariff={{ ...TEST_TARIFF, pods: [TEST_POD_WITH_OCPP_SUPPORT] }}
      />
    );

    expect(screen.getByRole('button', { name: 'Add schedule' })).toBeDisabled();
  });

  it.each(['Public', 'Driver', 'Member'])(
    'should be disabled and show tooltip on hover if a schedule is present for that tariff tier',
    async (tier) => {
      render(
        <AddFlatRateTariffScheduleModal
          {...defaultProps}
          tariff={TEST_TARIFF}
        />
      );

      fireEvent.click(screen.getByLabelText('Tariff tier'));
      fireEvent.click(screen.getByRole('option', { name: tier }));

      expect(
        screen.getByRole('button', { name: 'Add schedule' })
      ).toBeDisabled();
      await userEvent.hover(screen.getByRole('tooltip'));
      expect(
        await screen.findByText(
          'Any existing schedules for this tariff tier must be deleted before this schedule can be added'
        )
      ).toBeInTheDocument();
    }
  );
});
