import { COMMON_CURRENCY_PENCE_ERROR } from '@experience/shared/typescript/validation';
import { ErrorBoundary } from '@sentry/react';
import {
  TARIFF_INCOMPATIBLE_PRICING_MODEL_ERROR,
  TARIFF_INVALID_TARIFF_PRICING_MODEL_ERROR,
  TARIFF_INVALID_TARIFF_TIER_ERROR,
  TARIFF_PER_KWH_PRICE_ERROR,
  TARIFF_PRICE_MUST_NOT_BE_LESS_THAN_1P_IF_NOT_FREE_ERROR,
  TARIFF_SCHEDULE_LIMIT_ERROR,
  TARIFF_SCHEDULE_OVERLAP_ERROR,
  TariffErrorCodes,
  TariffScheduleErrorCodes,
} from '@experience/commercial/site-admin/typescript/domain-model-validation';
import {
  TEST_POD,
  TEST_POD_WITH_OCPP_SUPPORT,
  TEST_TARIFF,
  TEST_TARIFF_WITH_EMPTY_SCHEDULES,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { mutate } from 'swr';
import AddCustomTariffScheduleModal from './add-custom-tariff-schedule-modal';
import axios from 'axios';

jest.mock('axios');
const mockAxiosPost = jest.mocked(axios.post);

jest.mock('swr');
const mockMutate = jest.mocked(mutate);

const mockSetOpen = jest.fn();

const defaultProps = {
  tariff: { ...TEST_TARIFF_WITH_EMPTY_SCHEDULES, pods: [] },
  open: true,
  setOpen: mockSetOpen,
};

describe('AddCustomTariffScheduleModal', () => {
  it('should render correctly', () => {
    const { baseElement } = render(
      <AddCustomTariffScheduleModal {...defaultProps} />
    );

    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(
      <AddCustomTariffScheduleModal {...defaultProps} />
    );

    expect(baseElement).toMatchSnapshot();
  });

  it('should close modal and clear inputs if cancel button is clicked', () => {
    render(<AddCustomTariffScheduleModal {...defaultProps} />);

    fireEvent.click(screen.getByLabelText('Tariff tier'));
    fireEvent.click(screen.getByRole('option', { name: 'Member' }));

    fireEvent.click(screen.getByLabelText('Pricing model'));
    fireEvent.click(screen.getByRole('option', { name: 'per charge' }));

    fireEvent.click(screen.getByLabelText('Start day'));
    fireEvent.click(screen.getByRole('option', { name: 'Wednesday' }));

    fireEvent.change(screen.getByLabelText('Start time'), {
      target: { value: '09:00' },
    });
    expect(screen.getByLabelText('Start time')).toHaveValue('09:00');

    fireEvent.click(screen.getByLabelText('End day'));
    fireEvent.click(screen.getByRole('option', { name: 'Friday' }));

    fireEvent.change(screen.getByLabelText('End time'), {
      target: { value: '17:00' },
    });

    fireEvent.change(screen.getByLabelText('Price (in pence)'), {
      target: { value: '20' },
    });

    fireEvent.click(screen.getByRole('button', { name: 'Cancel' }));

    expect(mockSetOpen).toHaveBeenCalledWith(false);
    expect(
      screen.getByRole('button', { name: 'Tariff tier' })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('button', {
        name: 'Pricing model',
      })
    ).toBeInTheDocument();
    expect(screen.getByLabelText('Start time')).toHaveValue('00:00:00');
    expect(screen.getByRole('button', { name: 'End day' })).toBeInTheDocument();
    expect(screen.getByLabelText('End time')).toHaveValue('00:00:00');
    expect(screen.getByLabelText('Price (in pence)')).toHaveValue(undefined);

    fireEvent.click(screen.getByLabelText('Start day'));
    expect(
      screen.getByRole('option', { name: 'Monday Checkmark' })
    ).toBeInTheDocument();
    fireEvent.click(screen.getByRole('option', { name: 'Tuesday' }));

    fireEvent.click(screen.getByLabelText('End day'));
    expect(
      screen.getByRole('option', { name: 'Monday Checkmark' })
    ).toBeInTheDocument();
  });

  it('should render prepopulated default values', () => {
    render(<AddCustomTariffScheduleModal {...defaultProps} />);

    fireEvent.click(screen.getByLabelText('Start day'));
    expect(
      screen.getByRole('option', { name: 'Monday Checkmark' })
    ).toBeInTheDocument();
    fireEvent.click(screen.getByRole('option', { name: 'Tuesday' }));

    expect(screen.getByLabelText('Start time')).toHaveValue('00:00:00');

    fireEvent.click(screen.getByLabelText('End day'));
    expect(
      screen.getByRole('option', { name: 'Monday Checkmark' })
    ).toBeInTheDocument();

    expect(screen.getByLabelText('End time')).toHaveValue('00:00:00');
  });

  it('should disable per charge and per hour pricing models if tariff has public chargers assigned', () => {
    render(
      <AddCustomTariffScheduleModal
        {...defaultProps}
        tariff={{ ...TEST_TARIFF, pods: [{ ...TEST_POD, isPublic: true }] }}
      />
    );

    fireEvent.click(screen.getByLabelText('Pricing model'));
    expect(
      screen.getByRole('option', {
        name: 'per charge - incompatible with public chargers',
      })
    ).toHaveAttribute('aria-disabled', 'true');
    expect(
      screen.getByRole('option', {
        name: 'per hour - incompatible with public chargers',
      })
    ).toHaveAttribute('aria-disabled', 'true');
  });

  it('should not disable per charge and per hour pricing models if tariff does not have public chargers assigned', () => {
    render(
      <AddCustomTariffScheduleModal
        {...defaultProps}
        tariff={{ ...TEST_TARIFF, pods: [{ ...TEST_POD, isPublic: false }] }}
      />
    );

    fireEvent.click(screen.getByLabelText('Pricing model'));
    expect(
      screen.getByRole('option', {
        name: 'per charge',
      })
    ).not.toHaveAttribute('aria-disabled', 'true');
    expect(
      screen.getByRole('option', {
        name: 'per hour (plugged in)',
      })
    ).not.toHaveAttribute('aria-disabled', 'true');
  });

  it.each([
    ['a positive value', '1'],
    ['a value of zero', '0'],
  ])(
    'should call the API when the form is submitted when price is %s',
    async (_, price) => {
      render(<AddCustomTariffScheduleModal {...defaultProps} />);

      const submitButton = screen.getByRole('button', { name: 'Add schedule' });

      fireEvent.click(screen.getByLabelText('Tariff tier'));
      fireEvent.click(screen.getByRole('option', { name: 'Member' }));

      fireEvent.click(screen.getByLabelText('Pricing model'));
      fireEvent.click(screen.getByRole('option', { name: 'per charge' }));

      fireEvent.click(screen.getByLabelText('Start day'));
      fireEvent.click(screen.getByRole('option', { name: 'Wednesday' }));

      fireEvent.change(screen.getByLabelText('Start time'), {
        target: { value: '09:00' },
      });

      fireEvent.click(screen.getByLabelText('End day'));
      fireEvent.click(screen.getByRole('option', { name: 'Friday' }));

      fireEvent.change(screen.getByLabelText('End time'), {
        target: { value: '17:00' },
      });

      fireEvent.change(screen.getByLabelText('Price (in pence)'), {
        target: { value: price },
      });

      fireEvent.click(submitButton);

      expect(submitButton).toBeDisabled();

      await waitFor(() => {
        expect(mockAxiosPost).toHaveBeenCalledWith(
          `/api/tariffs/${TEST_TARIFF.id}/schedules`,
          {
            endDay: 5,
            endTime: '17:00',
            price: price,
            pricingModel: 'fixed',
            startDay: 3,
            startTime: '09:00',
            tariffTier: 'members',
          }
        );
        expect(mockMutate).toHaveBeenCalledWith(
          `/api/tariffs/${TEST_TARIFF.id}`
        );
        expect(mockSetOpen).toHaveBeenCalledWith(false);
        expect(submitButton).not.toBeDisabled();
      });
    }
  );

  it.each([
    ['per charge', '10.1', COMMON_CURRENCY_PENCE_ERROR],
    ['per hour (plugged in)', '10.1', COMMON_CURRENCY_PENCE_ERROR],
    ['per kWh', '10.1234', TARIFF_PER_KWH_PRICE_ERROR],
    [
      'per kWh',
      '0.999',
      TARIFF_PRICE_MUST_NOT_BE_LESS_THAN_1P_IF_NOT_FREE_ERROR,
    ],
    [
      'per kWh',
      '0.001',
      TARIFF_PRICE_MUST_NOT_BE_LESS_THAN_1P_IF_NOT_FREE_ERROR,
    ],
  ])(
    'should throw a validation error if the price is invalid for the price %s',
    async (type, value, error) => {
      render(<AddCustomTariffScheduleModal {...defaultProps} />);

      fireEvent.click(screen.getByLabelText('Pricing model'));
      fireEvent.click(screen.getByRole('option', { name: type }));

      fireEvent.change(screen.getByLabelText('Price (in pence)'), {
        target: { value },
      });

      fireEvent.click(screen.getByRole('button', { name: 'Add schedule' }));

      expect(await screen.findByText(error)).toBeInTheDocument();
    }
  );

  it.each([
    ['per hour (plugged in)', '1000', '£10.00'],
    ['per kWh', '2001', '£2.00'],
    ['per charge', '3001', '£30.00'],
  ])(
    'should throw a validation error if the pricing model is %s and price is %p',
    async (pricingModel, price, limit) => {
      render(<AddCustomTariffScheduleModal {...defaultProps} />);

      fireEvent.click(screen.getByLabelText('Pricing model'));
      fireEvent.click(screen.getByRole('option', { name: pricingModel }));

      fireEvent.change(screen.getByLabelText('Price (in pence)'), {
        target: { value: price },
      });

      fireEvent.click(screen.getByRole('button', { name: 'Add schedule' }));
      expect(
        await screen.findByText(TARIFF_SCHEDULE_LIMIT_ERROR(limit))
      ).toBeInTheDocument();
    }
  );

  it('should throw a validation error if incompatible pricing model error is thrown', async () => {
    mockAxiosPost.mockRejectedValueOnce({
      response: {
        data: {
          error: TariffErrorCodes.TARIFF_INCOMPATIBLE_PRICING_MODEL_ERROR,
          message: TARIFF_INCOMPATIBLE_PRICING_MODEL_ERROR,
          statusCode: 400,
        },
        status: 400,
      },
    });

    render(<AddCustomTariffScheduleModal {...defaultProps} />);

    fireEvent.click(screen.getByLabelText('Tariff tier'));
    fireEvent.click(screen.getByRole('option', { name: 'Member' }));

    fireEvent.click(screen.getByLabelText('Pricing model'));
    fireEvent.click(screen.getByRole('option', { name: 'per charge' }));

    fireEvent.click(screen.getByLabelText('Start day'));
    fireEvent.click(screen.getByRole('option', { name: 'Wednesday' }));

    fireEvent.change(screen.getByLabelText('Start time'), {
      target: { value: '09:00' },
    });

    fireEvent.click(screen.getByLabelText('End day'));
    fireEvent.click(screen.getByRole('option', { name: 'Friday' }));

    fireEvent.change(screen.getByLabelText('End time'), {
      target: { value: '17:00' },
    });

    fireEvent.change(screen.getByLabelText('Price (in pence)'), {
      target: { value: '20' },
    });

    fireEvent.click(screen.getByRole('button', { name: 'Add schedule' }));

    expect(
      await screen.findByText(TARIFF_INCOMPATIBLE_PRICING_MODEL_ERROR)
    ).toBeInTheDocument();
  });

  it('should handle errors and direct to error boundary', async () => {
    mockAxiosPost.mockRejectedValueOnce({
      response: {
        status: 500,
      },
    });

    render(
      <ErrorBoundary fallback={<h1>Error detected</h1>}>
        <AddCustomTariffScheduleModal {...defaultProps} />
      </ErrorBoundary>
    );

    fireEvent.click(screen.getByLabelText('Tariff tier'));
    fireEvent.click(screen.getByRole('option', { name: 'Member' }));

    fireEvent.click(screen.getByLabelText('Pricing model'));
    fireEvent.click(screen.getByRole('option', { name: 'per charge' }));

    fireEvent.click(screen.getByLabelText('Start day'));
    fireEvent.click(screen.getByRole('option', { name: 'Wednesday' }));

    fireEvent.change(screen.getByLabelText('Start time'), {
      target: { value: '09:00' },
    });

    fireEvent.click(screen.getByLabelText('End day'));
    fireEvent.click(screen.getByRole('option', { name: 'Friday' }));

    fireEvent.change(screen.getByLabelText('End time'), {
      target: { value: '17:00' },
    });

    fireEvent.change(screen.getByLabelText('Price (in pence)'), {
      target: { value: '20' },
    });

    fireEvent.click(screen.getByRole('button', { name: 'Add schedule' }));

    expect(
      await screen.findByRole('heading', { level: 1, name: 'Error detected' })
    ).toBeInTheDocument();
  });

  it('should throw a validation error for each of the fields', async () => {
    render(<AddCustomTariffScheduleModal {...defaultProps} />);

    fireEvent.click(screen.getByRole('button', { name: 'Add schedule' }));

    expect(
      await screen.findByText(TARIFF_INVALID_TARIFF_TIER_ERROR)
    ).toBeInTheDocument();
    expect(
      await screen.findByText(TARIFF_INVALID_TARIFF_PRICING_MODEL_ERROR)
    ).toBeInTheDocument();
    expect(await screen.findAllByText('This field is required')).toHaveLength(
      1
    );
  });

  it.each(['Driver', 'Member', 'Public'])(
    'should pre-populate and disable the pricing select if there is already a schedule for the tariff tier %s',
    (tier) => {
      const propsWithSchedules = { ...defaultProps, tariff: TEST_TARIFF };

      const { getByLabelText, getByRole } = render(
        <AddCustomTariffScheduleModal {...propsWithSchedules} />
      );

      fireEvent.click(getByLabelText('Tariff tier'));
      fireEvent.click(getByRole('option', { name: tier }));

      expect(getByLabelText('Pricing model')).toBeDisabled();
      expect(
        getByRole('button', { name: 'Pricing model' })
      ).toBeInTheDocument();
    }
  );

  it('should show validation message on form submission when the tariff overlaps with an existing one', async () => {
    mockAxiosPost.mockRejectedValueOnce({
      response: {
        data: {
          error: TariffScheduleErrorCodes.SCHEDULE_OVERLAP_ERROR,
          message: TARIFF_SCHEDULE_OVERLAP_ERROR,
          statusCode: 403,
        },
        status: 403,
      },
    });

    render(<AddCustomTariffScheduleModal {...defaultProps} />);

    fireEvent.click(screen.getByLabelText('Tariff tier'));
    fireEvent.click(screen.getByRole('option', { name: 'Member' }));

    fireEvent.click(screen.getByLabelText('Pricing model'));
    fireEvent.click(screen.getByRole('option', { name: 'per charge' }));

    fireEvent.click(screen.getByLabelText('Start day'));
    fireEvent.click(screen.getByRole('option', { name: 'Wednesday' }));

    fireEvent.change(screen.getByLabelText('Start time'), {
      target: { value: '09:00' },
    });

    fireEvent.click(screen.getByLabelText('End day'));
    fireEvent.click(screen.getByRole('option', { name: 'Friday' }));

    fireEvent.change(screen.getByLabelText('End time'), {
      target: { value: '17:00' },
    });

    fireEvent.change(screen.getByLabelText('Price (in pence)'), {
      target: { value: '20' },
    });

    fireEvent.click(screen.getByRole('button', { name: 'Add schedule' }));

    expect(
      await screen.findByText(TARIFF_SCHEDULE_OVERLAP_ERROR)
    ).toBeInTheDocument();
    expect(screen.getByLabelText('Start time')).toHaveClass(
      'border-2 border-solid border-error'
    );
    expect(screen.getByLabelText('End day')).toHaveClass(
      'border-2 border-solid border-error'
    );
    expect(screen.getByLabelText('End time')).toHaveClass(
      'border-2 border-solid border-error'
    );
  });

  it('should autofocus the first input', () => {
    render(<AddCustomTariffScheduleModal {...defaultProps} />);
    expect(screen.getByLabelText('Tariff tier')).toHaveFocus();
  });

  it('should disable confirm button if charger supports ocpp', () => {
    render(
      <AddCustomTariffScheduleModal
        {...defaultProps}
        tariff={{ ...TEST_TARIFF, pods: [TEST_POD_WITH_OCPP_SUPPORT] }}
      />
    );

    expect(screen.getByRole('button', { name: 'Add schedule' })).toBeDisabled();
  });
});
