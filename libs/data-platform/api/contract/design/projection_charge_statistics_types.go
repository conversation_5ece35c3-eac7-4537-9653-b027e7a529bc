package design

import (
	"fmt"

	. "goa.design/goa/v3/dsl"
	"goa.design/goa/v3/expr"
)

var SiteChargeStatisticsResponse = chargeStatisticsResponse(siteChargeStatisticsSchema, "site")
var GroupChargeStatisticsResponse = chargeStatisticsResponse(groupChargeStatisticsSchema, "group")
var ChargerChargeStatisticsResponse = chargeStatisticsResponse(chargerChargeStatisticsSchema, "charger")

func chargeStatisticsResponse(responseData interface{}, statsView string) expr.UserType {
	return Type(fmt.Sprintf("Projection%sChargeStatisticsResponse", statsView), func() {
		Attribute("data", responseData)
		Attribute("meta", ResponseMeta)
		Required("data", "meta")
	})
}

var siteChargeStatisticsSchema = Type("SiteChargeStatistics", func() {
	Attribute("numberOfChargers", Int, func() {
		Description("count of distinct charger ids")
		Example(3)
	})
	CoreStatistics()
	Required("numberOfCharges", "co2Savings", "energy", "numberOfChargers", "numberOfUsers", "chargingDuration", "revenueGenerated")
})

var groupChargeStatisticsSchema = Type("GroupChargeStatistics", func() {
	CoreStatistics()
	Attribute("numberOfChargers", Int, func() {
		Description("count of distinct charger ids")
		Example(3)
	})
	Attribute("numberOfSites", Int, func() {
		Description("count of distinct sites")
		Example(3)
	})
	Required("numberOfCharges", "numberOfSites", "co2Savings", "energy", "numberOfChargers", "numberOfUsers", "chargingDuration", "revenueGenerated")
})

var chargerChargeStatisticsSchema = Type("ChargerChargeStatistics", func() {
	CoreStatistics()
	Required("numberOfCharges", "numberOfUsers", "chargingDuration", "revenueGenerated", "co2Savings", "energy")
})

func CoreStatistics() {
	Attribute("chargingDuration", Int, func() {
		Description("sum of charging duration in seconds")
		Example(22245)
	})
	Attribute("co2Savings", Float64, func() {
		Description("CO2 saved in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh")
		Example(4325.62)
	})
	Attribute("energy", energyStatistics)
	Attribute("numberOfCharges", Int, func() {
		Description("count of distinct charge uuids")
		Example(10)
	})
	Attribute("numberOfUsers", Int, func() {
		Description("count of distinct user ids")
		Example(2)
	})
	Attribute("revenueGenerated", Int, func() {
		Description("sum of settlement amount in pence")
		Example(457600)
	})
}

var energyStatistics = Type("energyStatistics", func() {
	Attribute("totalUsage", Float64, func() {
		Description("Energy used this period in kWh.")
		Example(567.89)
	})
	Attribute("claimedUsage", Float64, func() {
		Description("Energy used this period in kWh, filtering out any unclaimed charges.")
		Example(243.89)
	})
	Attribute("revenueGeneratingClaimedUsage", Float64, func() {
		Description("Energy used this period in kWh by claimed charges with a positive revenue.")
		Example(43.50)
	})
	Attribute("unclaimedUsage", Float64, func() {
		Description("Energy used this period in kWh, filtering out any claimed charges.")
		Example(324.65)
	})
	Attribute("cost", Int, func() {
		Description("Energy cost in pence.")
		Example(324550)
	})
	Required("totalUsage", "claimedUsage", "revenueGeneratingClaimedUsage", "unclaimedUsage", "cost")
})
